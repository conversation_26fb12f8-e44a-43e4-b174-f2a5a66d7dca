package com.sayweee.datasync.dao;

import com.sayweee.datasync.model.entity.CommentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommentDao {

    void createTempTableLike(@Param("tempTable") String tempTable, @Param("targetTable") String targetTable);

    void batchInsert(@Param("tableName") String tableName, @Param("comments") List<CommentEntity> comments);

    int deleteFromTargetUsingTemp(@Param("targetTable") String targetTable, @Param("tempTable") String tempTable);

    int insertFromTempToTarget(@Param("targetTable") String targetTable, @Param("tempTable") String tempTable);

    void dropTable(@Param("tableName") String tableName);
}