package com.sayweee.datasync.dao;

import com.sayweee.datasync.model.entity.IssueEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IssueTempDao {
    /**
     * 批量插入 JIRA Issue 数据到动态指定的临时表。
     */
    void batchInsert(@Param("tableName") String tableName,
                     @Param("payloads") List<IssueEntity> payloads);

    /**
     * [新增] 从目标表中删除所有在临时表中也存在的记录。
     * @return 返回删除的行数。
     */
    int deleteFromTargetUsingTemp(@Param("targetTable") String targetTable,
                                  @Param("tempTable") String tempTable);

    /**
     * [新增] 将临时表中的所有记录插入到目标表中。
     * @return 返回插入的行数。
     */
    int insertFromTempToTarget(@Param("targetTable") String targetTable,
                               @Param("tempTable") String tempTable);

    /**
     * [可选] 创建一个与目标表结构相同的临时表。
     */
    void createTempTableLike(@Param("tempTable") String tempTable,
                             @Param("targetTable") String targetTable);

    /**
     * [可选] 删除指定的表。
     */
    void dropTable(@Param("tableName") String tableName);

    /**
     * 优化版本：分批批量插入，支持更好的类型处理
     */
    void batchInsertChunked(@Param("tableName") String tableName,
                           @Param("payloads") List<IssueEntity> payloads);

    /**
     * 使用 MERGE 语句进行 UPSERT 操作（如果数据库支持）
     */
    int mergeFromTempToTarget(@Param("targetTable") String targetTable,
                             @Param("tempTable") String tempTable);
}
