package com.sayweee.datasync.rest;

import com.sayweee.core.framework.base.BaseResponse;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.model.response.SyncResponse;
import com.sayweee.datasync.service.AsyncSyncService;
import com.sayweee.datasync.service.jira.IssueService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping()
@RequiredArgsConstructor
public class SyncRest {

    private final AsyncSyncService asyncSyncService;
    private final IssueService issueService;

    @PostMapping("/jira")
    public BaseResponse<SyncResponse> startJiraSync(@RequestBody JiraSyncRequest request) {
        request.applyDefaults();
        var taskId = asyncSyncService.submitJiraSync(request);
        var response = SyncResponse.success(taskId, "Jira同步任务已提交，正在后台执行");
        return BaseResponse.success(response);
    }

    /**
     * 同步JIRA数据到数据库
     */
    @PostMapping("/jira/sync-to-database")
    public BaseResponse<Map<String, Object>> syncJiraToDatabase(@RequestBody JiraSyncRequest request) {
        request.applyDefaults();

        String taskId = "jira-db-sync-" + System.currentTimeMillis();

        issueService.syncIssues(taskId, request);
        return BaseResponse.success(new HashMap<>() {
            {
                put("taskId", taskId);
                put("status", "started");
                put("message", "Jira同步任务已提交，正在后台执行");
                put("startTime", System.currentTimeMillis());
            }
        });
    }
}