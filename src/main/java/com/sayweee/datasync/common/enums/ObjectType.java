package com.sayweee.datasync.common.enums;

/**
 * JIRA 对象类型枚举
 * 定义支持同步的 JIRA 对象类型
 */
public enum ObjectType {
    /**
     * JIRA 问题/工单
     */
    ISSUE("issue"),
    
    /**
     * 工作日志
     */
    WORKLOG("worklog"),
    
    /**
     * 项目
     */
    PROJECT("project"),
    
    /**
     * 用户
     */
    USER("user");
    
    private final String value;
    
    ObjectType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    /**
     * 根据字符串值获取对应的枚举
     */
    public static ObjectType fromValue(String value) {
        for (ObjectType type : ObjectType.values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown object type: " + value);
    }
}