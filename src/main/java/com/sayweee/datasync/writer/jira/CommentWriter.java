package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.model.entity.CommentEntity;
import com.sayweee.datasync.dao.CommentDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class CommentWriter {

    private final CommentDao commentDao;

    public void writeComments(List<CommentEntity> commentEntities) {
        if (commentEntities == null || commentEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertComments(commentEntities);
            log.info("Successfully processed {} comments", affectedRows);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write comments to database", e);
        }
    }

    private int batchUpsertComments(List<CommentEntity> commentEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, commentEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_issue_comments_" + System.currentTimeMillis();
        try {
            commentDao.createTempTableLike(tempTableName, "weee_jira_new.issue_comments");
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    private void batchInsertToTempTable(String tempTableName, List<CommentEntity> commentEntities) {
        try {
            commentDao.batchInsert(tempTableName, commentEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table", e);
        }
    }

    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = "weee_jira_new.issue_comments";
            int deletedRows = commentDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = commentDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation", e);
        }
    }

    private void dropTempTable(String tempTableName) {
        try {
            commentDao.dropTable(tempTableName);
        } catch (Exception e) {
            log.warn("Failed to drop temp table: {}", tempTableName, e);
        }
    }
}