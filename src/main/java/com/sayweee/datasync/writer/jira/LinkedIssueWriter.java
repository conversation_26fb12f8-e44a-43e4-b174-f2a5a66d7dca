package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.common.constants.SyncConstants;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import com.sayweee.datasync.dao.LinkedIssueDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class LinkedIssueWriter {

    private final LinkedIssueDao linkedIssueDao;

    /**
     * 批量写入或更新 Redshift 中的 JIRA Linked Issues
     */
    public void write(List<LinkedIssueEntity> linkedIssueEntities) {
        if (linkedIssueEntities == null || linkedIssueEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertLinkedIssues(linkedIssueEntities);
            log.info("Successfully processed {} linked issues", affectedRows);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write linked issues to database", e);
        }
    }

    /**
     * 批量 UPSERT Linked Issues 到 Redshift (完全使用 MyBatis)
     */
    private int batchUpsertLinkedIssues(List<LinkedIssueEntity> linkedIssueEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, linkedIssueEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    /**
     * 【MyBatis 实现】创建临时表
     */
    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_linked_issues_" + System.currentTimeMillis();
        try {
            linkedIssueDao.createTempTableLike(tempTableName, SyncConstants.Database.JIRA_LINKED_ISSUES_TABLE);
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    /**
     * 【MyBatis 实现】批量插入数据到临时表
     */
    private void batchInsertToTempTable(String tempTableName, List<LinkedIssueEntity> linkedIssueEntities) {
        try {
            linkedIssueDao.batchInsert(tempTableName, linkedIssueEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】执行 UPSERT 操作 (DELETE + INSERT)
     */
    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = SyncConstants.Database.JIRA_LINKED_ISSUES_TABLE;
            int deletedRows = linkedIssueDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = linkedIssueDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】清理临时表
     */
    private void dropTempTable(String tempTableName) {
        try {
            linkedIssueDao.dropTable(tempTableName);
        } catch (Exception e) {
            log.warn("Failed to drop temp table: {}", tempTableName, e);
        }
    }
}
