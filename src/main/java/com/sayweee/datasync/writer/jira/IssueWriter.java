package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.common.constants.SyncConstants;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.dao.IssueTempDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class IssueWriter {

    private final IssueTempDao issueTempDao;

    /**
     * 批量写入或更新 Redshift 中的 JIRA Issues
     */
    public void write(List<IssueEntity> issueEntities) {
        if (issueEntities == null || issueEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertIssues(issueEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write issues to database", e);
        }
    }

    /**
     * 批量 UPSERT Issues 到 Redshift (完全使用 MyBatis)
     */
    private int batchUpsertIssues(List<IssueEntity> issueEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, issueEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    /**
     * 【MyBatis 实现】创建临时表
     */
    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_jira_issues_" + "_" + System.currentTimeMillis();
        try {
            issueTempDao.createTempTableLike(tempTableName, "weee_jira_new.issue");
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    /**
     * 【MyBatis 实现】批量插入数据到临时表
     */
    private void batchInsertToTempTable(String tempTableName, List<IssueEntity> issueEntities) {
        try {
            issueTempDao.batchInsert(tempTableName, issueEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】执行 UPSERT 操作 (DELETE + INSERT)
     */
    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = SyncConstants.Database.JIRA_ISSUES_TABLE;
            int deletedRows = issueTempDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = issueTempDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation with MyBatis", e);
        }
    }

    /**
     * 【MyBatis 实现】清理临时表
     */
    private void dropTempTable(String tempTableName) {
        try {
            issueTempDao.dropTable(tempTableName);
        } catch (Exception e) {
        }
    }
}