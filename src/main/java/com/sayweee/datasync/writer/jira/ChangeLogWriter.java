package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.dao.ChangeLogDao;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ChangeLogWriter {

    private final ChangeLogDao changeLogDao;

    public void writeChangeLogs(List<ChangeLogEntity> changeLogEntities) {
        if (changeLogEntities == null || changeLogEntities.isEmpty()) {
            return;
        }

        try {
            int affectedRows = batchUpsertChangeLogs(changeLogEntities);
            log.info("Successfully processed {} change logs", affectedRows);
        } catch (Exception e) {
            throw new RuntimeException("Failed to write change logs to database", e);
        }
    }

    private int batchUpsertChangeLogs(List<ChangeLogEntity> changeLogEntities) {
        String tempTableName = createTempTable();
        try {
            batchInsertToTempTable(tempTableName, changeLogEntities);
            return executeUpsertFromTempTable(tempTableName);
        } finally {
            dropTempTable(tempTableName);
        }
    }

    private String createTempTable() {
        String tempTableName = "weee_jira_new.temp_issue_changelogs_" + System.currentTimeMillis();
        try {
            changeLogDao.createTempTableLike(tempTableName, "weee_jira_new.issue_changelogs");
            return tempTableName;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create temp table", e);
        }
    }

    private void batchInsertToTempTable(String tempTableName, List<ChangeLogEntity> changeLogEntities) {
        try {
            changeLogDao.batchInsert(tempTableName, changeLogEntities);
        } catch (Exception e) {
            throw new RuntimeException("Failed to insert data into temp table", e);
        }
    }

    private int executeUpsertFromTempTable(String tempTableName) {
        try {
            String targetTable = "weee_jira_new.issue_changelogs";
            int deletedRows = changeLogDao.deleteFromTargetUsingTemp(targetTable, tempTableName);
            int insertedRows = changeLogDao.insertFromTempToTarget(targetTable, tempTableName);
            return deletedRows + insertedRows;
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute UPSERT operation", e);
        }
    }

    private void dropTempTable(String tempTableName) {
        try {
            changeLogDao.dropTable(tempTableName);
        } catch (Exception e) {
            log.warn("Failed to drop temp table: {}", tempTableName, e);
        }
    }
}
