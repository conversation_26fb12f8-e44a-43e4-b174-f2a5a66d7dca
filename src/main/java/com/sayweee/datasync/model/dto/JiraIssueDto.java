package com.sayweee.datasync.model.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * JIRA Issue数据传输对象
 */
@Data
@Builder
public class JiraIssueDto {
    
    /**
     * Issue Key (如: PROJ-123)
     */
    private String key;
    
    /**
     * Issue ID
     */
    private Long id;
    
    /**
     * 标题
     */
    private String summary;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * Issue类型
     */
    private String issueType;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 优先级
     */
    private String priority;
    
    /**
     * 项目Key
     */
    private String projectKey;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 报告人
     */
    private String reporter;
    
    /**
     * 经办人
     */
    private String assignee;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdDate;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedDate;
    
    /**
     * 解决时间
     */
    private LocalDateTime resolvedDate;
    
    /**
     * 标签
     */
    private List<String> labels;
    
    /**
     * 组件
     */
    private List<String> components;
    
    /**
     * 修复版本
     */
    private List<String> fixVersions;
    
    /**
     * 影响版本
     */
    private List<String> affectedVersions;
    
    /**
     * 自定义字段
     */
    private Map<String, Object> customFields;
    
    /**
     * 评论数量
     */
    private Integer commentCount;
    
    /**
     * 附件数量
     */
    private Integer attachmentCount;
}