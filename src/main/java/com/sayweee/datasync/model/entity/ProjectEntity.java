package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * JIRA 项目实体类
 * 对应数据库表: weee_jira.projects
 */
@Getter
@Setter
public class ProjectEntity {

    /**
     * 项目ID
     */
    private String id;

    /**
     * 项目键值
     */
    private String key;

    /**
     * 项目URL
     */
    private String url;

    /**
     * 项目负责人
     */
    private String lead;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目自身引用URL
     */
    private String self;

    /**
     * 项目UUID
     */
    private String uuid;

    /**
     * 项目邮箱
     */
    private String email;

    /**
     * 项目角色信息
     */
    private String roles;

    /**
     * 项目样式
     */
    private String style;

    /**
     * 扩展信息
     */
    private String expand;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 洞察信息
     */
    private String insight;

    /**
     * 是否已归档
     */
    private Boolean archived;

    /**
     * 版本信息
     */
    private String versions;

    /**
     * 删除者
     */
    private String deletedBy;

    /**
     * 是否收藏
     */
    private Boolean favourite;

    /**
     * 是否私有
     */
    private Boolean isPrivate;

    /**
     * 归档者
     */
    private String archivedBy;

    /**
     * 头像URL信息
     */
    private String avatarUrls;

    /**
     * 组件信息
     */
    private String components;

    /**
     * Issue类型信息
     */
    private String issueTypes;

    /**
     * 属性信息
     */
    private String properties;

    /**
     * 是否简化模式
     */
    private Boolean simplified;

    /**
     * 删除日期 (带时区)
     */
    private OffsetDateTime deletedDate;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 权限信息
     */
    private String permissions;

    /**
     * 归档日期 (带时区)
     */
    private OffsetDateTime archivedDate;

    /**
     * 分配者类型
     */
    private String assigneeType;

    /**
     * 项目类型键值
     */
    private String projectTypeKey;

    /**
     * 项目分类
     */
    private String projectCategory;

    /**
     * 保留截止日期 (带时区)
     */
    private OffsetDateTime retentionTillDate;

    /**
     * Issue类型层次结构
     */
    private String issueTypeHierarchy;

    /**
     * Airbyte AB ID
     */
    private String airbyteAbId;

    /**
     * Airbyte 发出时间 (带时区)
     */
    private OffsetDateTime airbyteEmittedAt;

    /**
     * Airbyte 标准化时间 (不带时区)
     */
    private LocalDateTime airbyteNormalizedAt;

    /**
     * Airbyte 项目哈希ID
     */
    private String airbyteProjectsHashId;

    /**
     * 默认构造函数
     */
    public ProjectEntity() {
    }

    @Override
    public String toString() {
        return "ProjectEntity{" +
                "id='" + id + '\'' +
                ", key='" + key + '\'' +
                ", name='" + name + '\'' +
                ", lead='" + lead + '\'' +
                ", deleted=" + deleted +
                ", archived=" + archived +
                ", favourite=" + favourite +
                ", isPrivate=" + isPrivate +
                ", simplified=" + simplified +
                '}';
    }
}
