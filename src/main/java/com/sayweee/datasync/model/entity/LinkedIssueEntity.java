package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * JIRA Issue 关联问题实体类
 * 对应数据库表: weee_jira.issue_linked_issues
 */
@Getter
@Setter
public class LinkedIssueEntity {

    /**
     * Issue ID
     */
    private Integer issueId;

    /**
     * 故事键值 (主要 Issue 的 Key)
     */
    private String storyKey;

    /**
     * 关联 Issue 的键值
     */
    private String linkKey;

    /**
     * 关联类型 (如: blocks, is blocked by, relates to 等)
     */
    private String linkType;

    /**
     * 默认构造函数
     */
    public LinkedIssueEntity() {
    }

    /**
     * 全参数构造函数
     */
    public LinkedIssueEntity(Integer issueId, String storyKey, String linkKey, String linkType) {
        this.issueId = issueId;
        this.storyKey = storyKey;
        this.linkKey = linkKey;
        this.linkType = linkType;
    }

    @Override
    public String toString() {
        return "LinkedIssueEntity{" +
                "issueId=" + issueId +
                ", storyKey='" + storyKey + '\'' +
                ", linkKey='" + linkKey + '\'' +
                ", linkType='" + linkType + '\'' +
                '}';
    }
}
