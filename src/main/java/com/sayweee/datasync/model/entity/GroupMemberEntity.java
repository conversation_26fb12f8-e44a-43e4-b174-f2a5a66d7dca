package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * JIRA 组成员实体类
 * 对应数据库表: weee_jira.group_members
 */
@Getter
@Setter
public class GroupMemberEntity {

    /**
     * 组ID
     */
    private String groupId;

    /**
     * 组名称
     */
    private String groupName;

    /**
     * 用户账户ID
     */
    private String accountId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户是否激活
     */
    private Boolean active;

    /**
     * 默认构造函数
     */
    public GroupMemberEntity() {
    }

    /**
     * 全参数构造函数
     */
    public GroupMemberEntity(String groupId, String groupName, String accountId, 
                           String userName, String email, Boolean active) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.accountId = accountId;
        this.userName = userName;
        this.email = email;
        this.active = active;
    }

    @Override
    public String toString() {
        return "GroupMemberEntity{" +
                "groupId='" + groupId + '\'' +
                ", groupName='" + groupName + '\'' +
                ", accountId='" + accountId + '\'' +
                ", userName='" + userName + '\'' +
                ", email='" + email + '\'' +
                ", active=" + active +
                '}';
    }
}
