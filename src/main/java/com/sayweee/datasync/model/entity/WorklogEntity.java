package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * JIRA Issue 时间日志实体类
 * 对应数据库表: weee_jira.issue_timelog
 */
@Getter
@Setter
public class WorklogEntity {

    /**
     * 时间日志ID
     */
    private Integer id;

    /**
     * 创建时间 (带时区)
     */
    private OffsetDateTime created;

    /**
     * Issue ID
     */
    private Integer issueId;

    /**
     * 开始时间 (带时区)
     */
    private OffsetDateTime started;

    /**
     * 更新时间 (带时区)
     */
    private OffsetDateTime updated;

    /**
     * 花费时间描述 (如: 2h 30m)
     */
    private String timeSpent;

    /**
     * 花费时间秒数
     */
    private Integer timeSpentSeconds;

    /**
     * 工作日志评论
     */
    private String comment;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 数据入库时间 (不带时区)
     */
    private LocalDateTime inDate;

    /**
     * 默认构造函数
     */
    public WorklogEntity() {
    }

    /**
     * 全参数构造函数
     */
    public WorklogEntity(Integer id, OffsetDateTime created, Integer issueId,
                         OffsetDateTime started, OffsetDateTime updated, String timeSpent,
                         Integer timeSpentSeconds, String comment, String userId,
                         String userName, LocalDateTime inDate) {
        this.id = id;
        this.created = created;
        this.issueId = issueId;
        this.started = started;
        this.updated = updated;
        this.timeSpent = timeSpent;
        this.timeSpentSeconds = timeSpentSeconds;
        this.comment = comment;
        this.userId = userId;
        this.userName = userName;
        this.inDate = inDate;
    }

    @Override
    public String toString() {
        return "WorklogEntity{" +
                "id=" + id +
                ", created=" + created +
                ", issueId=" + issueId +
                ", started=" + started +
                ", updated=" + updated +
                ", timeSpent='" + timeSpent + '\'' +
                ", timeSpentSeconds=" + timeSpentSeconds +
                ", comment='" + comment + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", inDate=" + inDate +
                '}';
    }
}
