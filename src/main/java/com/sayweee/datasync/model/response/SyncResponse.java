package com.sayweee.datasync.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * 同步任务响应对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncResponse {

    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 创建时间
     */
    private Instant createdAt;
    
    /**
     * 创建成功响应
     */
    public static SyncResponse success(String taskId, String message) {
        return new SyncResponse(taskId, "ACCEPTED", message, Instant.now());
    }
    
    /**
     * 创建失败响应
     */
    public static SyncResponse error(String message) {
        return new SyncResponse(null, "ERROR", message, Instant.now());
    }
}