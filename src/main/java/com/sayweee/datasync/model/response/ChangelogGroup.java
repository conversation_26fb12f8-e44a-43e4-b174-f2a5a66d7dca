package com.sayweee.datasync.model.response;


import com.atlassian.jira.rest.client.api.domain.ChangelogItem;
import com.atlassian.jira.rest.client.api.domain.User;
import org.joda.time.DateTime;

import java.util.List;
import java.util.Objects;

public class ChangelogGroup extends com.atlassian.jira.rest.client.api.domain.ChangelogGroup {

    /**
     * 变更历史记录的唯一ID。
     */
    private final String id;

    /**
     * 默认构造函数。
     * 主要用于JSON反序列化框架（如 Jackson 或 Gson）进行对象映射。
     */
    public ChangelogGroup() {
        // 调用父类的默认构造函数（如果存在），并初始化id为null
        super(null, null, null);
        this.id = null;
    }

    /**
     * 全参数构造函数。
     *
     * @param id      变更历史记录的ID
     * @param author  进行变更的用户
     * @param created 变更发生的时间
     * @param items   本次变更中所有字段的修改详情列表
     */
    public ChangelogGroup(String id, User author, DateTime created, List<ChangelogItem> items) {
        // 通过 super() 调用父类的构造函数来初始化继承的字段
        super(author, created, items);
        this.id = id;
    }

    /**
     * 获取变更历史记录的ID。
     *
     * @return 变更历史ID
     */
    public String getId() {
        return id;
    }

    @Override
    public String toString() {
        return "ChangelogGroup{" +
                "id='" + id + '\'' +
                ", author=" + getAuthor() +
                ", created=" + getCreated() +
                ", items=" + getItems() +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false; // 检查父类字段是否相等
        ChangelogGroup that = (ChangelogGroup) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        // 结合父类的hashCode和当前类的id字段
        return Objects.hash(super.hashCode(), id);
    }
}
