package com.sayweee.datasync.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class RemoteLink {
    @JsonProperty("id")
    private long id;
    private String projectKey;
    private String remoteLink;

    @JsonProperty("self")
    private String self;
    @JsonProperty("object")
    private RemoteLinkObject object;

    @Override
    public String toString() {
        return "RemoteLink{" + "id=" + id + ", object=" + object + '}';
    }
}