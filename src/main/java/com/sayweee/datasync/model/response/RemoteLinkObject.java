package com.sayweee.datasync.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class RemoteLinkObject {
    @JsonProperty("url")
    private String url;
    @JsonProperty("title")
    private String title;

    @Override
    public String toString() {
        return "RemoteLinkObject{" + "url='" + url + '\'' + ", title='" + title + '\'' + '}';
    }
}