package com.sayweee.datasync.model.request;

import com.sayweee.datasync.common.enums.SyncMode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 数据同步请求对象
 * 用于接收外部的同步请求参数
 */
@Getter
@Setter
public class SyncRequest {
    
    /**
     * 开始日期 (UTC时间，格式: yyyy-MM-ddTHH:mm:ssZ 或 yyyy-MM-dd)
     */

}