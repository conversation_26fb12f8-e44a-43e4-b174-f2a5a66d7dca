package com.sayweee.datasync.config;

import com.atlassian.jira.rest.client.api.JiraRestClient;
import com.atlassian.jira.rest.client.internal.async.AsynchronousJiraRestClientFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;

/**
 * JIRA客户端配置
 */
@Slf4j
@Configuration
public class JiraConfig {
    
//    @Value("${jira.url:https://your-domain.atlassian.net}")
    private String jiraUrl ="https://sayweee.atlassian.net";
    
//    @Value("${jira.username:}")
    private String username="<EMAIL>";
    
//    @Value("${jira.token:}")
    private String apiToken="hgBBJ03dgxm8X5Q3UaEz1624";
    
    /**
     * 创建JIRA REST客户端
     */
    @Bean
    public JiraRestClient jiraRestClient() {
        try {
            var factory = new AsynchronousJiraRestClientFactory();
            var jiraServerUri = URI.create(jiraUrl);

            log.info("初始化JIRA客户端，连接到: {}", jiraUrl);

            // 使用用户名和API Token进行认证
            return factory.createWithBasicHttpAuthentication(jiraServerUri, username, apiToken);

        } catch (Exception e) {
            log.error("创建JIRA客户端失败", e);
            throw new RuntimeException("无法连接到JIRA服务器", e);
        }
    }
}