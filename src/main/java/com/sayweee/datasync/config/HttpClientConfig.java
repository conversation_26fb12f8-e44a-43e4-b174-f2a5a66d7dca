package com.sayweee.datasync.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.http.HttpClient;
import java.time.Duration;

@Configuration
public class HttpClientConfig {

    @Bean
    public HttpClient httpClient() {
        // 使用 HttpClient.newBuilder() 来进行详细配置
        return HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_2) // 优先使用 HTTP/2
                .connectTimeout(Duration.ofSeconds(10)) // 设置连接超时时间为 10 秒
                .followRedirects(HttpClient.Redirect.NORMAL) // 设置重定向策略
                .build();
    }
}