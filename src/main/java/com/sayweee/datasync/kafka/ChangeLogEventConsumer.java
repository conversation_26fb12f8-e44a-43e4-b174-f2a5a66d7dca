package com.sayweee.datasync.kafka;

import com.sayweee.datasync.model.entity.ChangeLogEntity;
import com.sayweee.datasync.service.jira.ChangeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Consumer;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class ChangeLogEventConsumer {
    private final ChangeLogService changeLogService;
    @Bean
    public Consumer<List<ChangeLogEntity>> consumeAndSaveChangeLog() {
        return events -> {
            if (events == null || events.isEmpty()) {
                return;
            }
            changeLogService.processAndSaveChangelog(events);
            log.info("Received a batch of {} events to process.", events.size());
        };
    }
}
