package com.sayweee.datasync.kafka;

import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import com.sayweee.datasync.service.jira.RemoteLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Consumer;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class RemoteLinkEventConsumer {
    private final RemoteLinkService remoteLinkService;
    @Bean
    public Consumer<List<RemoteLinkEntity>> consumeAndSaveRemoteLink() {
        return events -> {
            if (events == null || events.isEmpty()) {
                return;
            }
            remoteLinkService.processAndSaveRemoteLink(events);
            log.info("Received a batch of {} events to process.", events.size());
        };
    }
}
