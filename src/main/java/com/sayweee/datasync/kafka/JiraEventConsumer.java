package com.sayweee.datasync.kafka;

import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.service.jira.ChangeLogService;
import com.sayweee.datasync.service.jira.IssueService;
import com.sayweee.datasync.service.jira.RemoteLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Consumer;


@Configuration
@Slf4j
@RequiredArgsConstructor
public class JiraEventConsumer {
    private final IssueService issueService;
    private final ChangeLogService changeLogService;
    private final RemoteLinkService remoteLinkService;

    @Bean
    public Consumer<List<JiraIngestionPayload>> consumeAndSaveJiraIssue() {
        return events -> {
            if (events == null || events.isEmpty()) {
                return;
            }

            log.info("Received a batch of {} events to process.", events.size());

            // 强烈建议将整个批处理委托给 Service 层
            // 这样可以在 Service 层中统一管理事务
            try {
                // MDC 通常在批处理开始时设置一个，或者根本不设置
                // 如果需要追踪单条消息，逻辑会更复杂
                // 这里以批处理为单位进行追踪
//                var firstTraceId = events.get(0).metadata().traceId();
//                try (var ignored = MDC.putCloseable("traceId", firstTraceId + "-batch")) {
                    issueService.processAndSaveIssue(events);
//                }
            } catch (Exception e) {
                log.error("Error processing batch of Jira issues. Batch size: {}", events.size(), e);
                // 异常处理：根据你的消息中间件和重试策略，
                // 抛出异常可能会导致整个批次重试。
                throw new RuntimeException("Failed to process batch", e);
            }
        };
    }

    @Bean
    public Consumer<List<JiraIngestionPayload>> consumeAndFetchChangeLog() {
        return events -> {
            if (events == null || events.isEmpty()) {
                return;
            }

            log.info("Received a batch of {} events to process, processing changelog.", events.size());

            try {
                changeLogService.processAndFetchChangelog(events);
            } catch (Exception e) {
                log.error("Error processing batch of Jira issues. Batch size: {}", events.size(), e);
                throw new RuntimeException("Failed to process batch", e);
            }
        };
    }

    @Bean
    public Consumer<List<JiraIngestionPayload>> consumeAndFetchRemoteLink() {
        return events -> {
            if (events == null || events.isEmpty()) {
                return;
            }

            log.info("Received a batch of {} events to process, processing remote link", events.size());

            try {
                remoteLinkService.processAndFetchRemoteLink(events);
            } catch (Exception e) {
                log.error("Error processing batch of Jira issues. Batch size: {}", events.size(), e);
                throw new RuntimeException("Failed to process batch", e);
            }
        };
    }
}