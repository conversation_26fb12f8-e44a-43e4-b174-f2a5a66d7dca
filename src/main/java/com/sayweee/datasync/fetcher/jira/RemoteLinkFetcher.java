package com.sayweee.datasync.fetcher.jira;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sayweee.core.framework.util.JacksonUtils;
import com.sayweee.datasync.model.response.RemoteLink;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;
import java.util.List;


@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteLinkFetcher {
    //    @Value("${jira.url:https://your-domain.atlassian.net}")
    private String JIRA_SERVER_URI = "https://sayweee.atlassian.net";

    //    @Value("${jira.username:}")
    private String JIRA_EMAIL = "<EMAIL>";

    //    @Value("${jira.token:}")
    private String JIRA_API_TOKEN = "hgBBJ03dgxm8X5Q3UaEz1624";
    private final HttpClient httpClient;

    private List<RemoteLink> fetch(String issueKey) {
        try {
            String auth = JIRA_EMAIL + ":" + JIRA_API_TOKEN;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
            String authHeader = "Basic " + encodedAuth;

            URI remoteLinkUri = URI.create(JIRA_SERVER_URI.toString() + "/rest/api/3/issue/" + issueKey + "/remotelink");

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(remoteLinkUri)
                    .header("Authorization", authHeader)
                    .header("Accept", "application/json")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                return JacksonUtils.instance().readValue(response.body(), new TypeReference<>() { });
            } else {
                System.err.printf("Error fetching remote links for %s: Status %d, Body: %s%n",
                        issueKey, response.statusCode(), response.body());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }
}