package com.sayweee.datasync.fetcher.jira.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.core.framework.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * JIRA HTTP 客户端，用于发送HTTP请求到JIRA API
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JiraHttpClient {

    private final HttpClient httpClient;

    @Value("${jira.server.uri}")
    private URI JIRA_SERVER_URI;

    @Value("${jira.email}")
    private String JIRA_EMAIL;

    @Value("${jira.api.token}")
    private String JIRA_API_TOKEN;

    /**
     * 发送POST请求到JIRA API并返回JsonNode响应
     *
     * @param taskId 任务ID，用于日志记录
     * @param endpoint API端点路径（相对于JIRA服务器URI）
     * @param jsonPayload 请求体JSON数据
     * @return 解析后的JsonNode响应
     * @throws IOException 当HTTP请求失败或响应状态码不正确时抛出
     * @throws InterruptedException 当HTTP请求被中断时抛出
     */
    public JsonNode sendPostRequest(String taskId, String endpoint, String jsonPayload) 
            throws IOException, InterruptedException {
        
        String url = JIRA_SERVER_URI + endpoint;
        String authHeader = createAuthHeader();
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .header("Authorization", authHeader)
                .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();

        log.debug("Task [{}] - Sending POST request to: {}", taskId, url);
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            log.error("Task [{}] - HTTP request failed. Status: {}, Body: {}", 
                     taskId, response.statusCode(), response.body());
            throw new IOException("Task [" + taskId + "] - Unexpected response code: " + 
                                response.statusCode() + "\nBody: " + response.body());
        }

        log.debug("Task [{}] - HTTP request successful. Status: {}", taskId, response.statusCode());
        
        return JacksonUtils.instance().readTree(response.body());
    }

    /**
     * 发送GET请求到JIRA API并返回JsonNode响应
     *
     * @param taskId 任务ID，用于日志记录
     * @param endpoint API端点路径（相对于JIRA服务器URI）
     * @return 解析后的JsonNode响应
     * @throws IOException 当HTTP请求失败或响应状态码不正确时抛出
     * @throws InterruptedException 当HTTP请求被中断时抛出
     */
    public JsonNode sendGetRequest(String taskId, String endpoint) 
            throws IOException, InterruptedException {
        
        String url = JIRA_SERVER_URI + endpoint;
        String authHeader = createAuthHeader();
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Accept", "application/json")
                .header("Authorization", authHeader)
                .GET()
                .build();

        log.debug("Task [{}] - Sending GET request to: {}", taskId, url);
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            log.error("Task [{}] - HTTP request failed. Status: {}, Body: {}", 
                     taskId, response.statusCode(), response.body());
            throw new IOException("Task [" + taskId + "] - Unexpected response code: " + 
                                response.statusCode() + "\nBody: " + response.body());
        }

        log.debug("Task [{}] - HTTP request successful. Status: {}", taskId, response.statusCode());
        
        return JacksonUtils.instance().readTree(response.body());
    }

    /**
     * 发送原始HTTP请求并返回字符串响应
     *
     * @param taskId 任务ID，用于日志记录
     * @param request 预构建的HttpRequest对象
     * @return HTTP响应字符串
     * @throws IOException 当HTTP请求失败时抛出
     * @throws InterruptedException 当HTTP请求被中断时抛出
     */
    public String sendRawRequest(String taskId, HttpRequest request) 
            throws IOException, InterruptedException {
        
        log.debug("Task [{}] - Sending {} request to: {}", 
                 taskId, request.method(), request.uri());
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            log.error("Task [{}] - HTTP request failed. Status: {}, Body: {}", 
                     taskId, response.statusCode(), response.body());
            throw new IOException("Task [" + taskId + "] - Unexpected response code: " + 
                                response.statusCode() + "\nBody: " + response.body());
        }

        log.debug("Task [{}] - HTTP request successful. Status: {}", taskId, response.statusCode());
        
        return response.body();
    }

    /**
     * 创建JIRA API认证头
     *
     * @return Base64编码的认证头字符串
     */
    private String createAuthHeader() {
        String credentials = JIRA_EMAIL + ":" + JIRA_API_TOKEN;
        return "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 获取JIRA服务器URI
     *
     * @return JIRA服务器URI
     */
    public URI getJiraServerUri() {
        return JIRA_SERVER_URI;
    }

    /**
     * 创建带认证的HttpRequest.Builder
     *
     * @param uri 请求URI
     * @return 预配置的HttpRequest.Builder
     */
    public HttpRequest.Builder createAuthenticatedRequestBuilder(URI uri) {
        return HttpRequest.newBuilder()
                .uri(uri)
                .header("Authorization", createAuthHeader())
                .header("Accept", "application/json");
    }
}
