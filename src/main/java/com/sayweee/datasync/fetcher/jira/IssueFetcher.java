package com.sayweee.datasync.fetcher.jira;

import com.atlassian.jira.rest.client.api.JiraRestClient;
import com.atlassian.jira.rest.client.api.domain.SearchResult;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.fetcher.jira.parser.JiraPayloadParser;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class IssueFetcher {
    private static final int MAX_RESULTS_PER_PAGE = 50;
    private record JiraPagedState(List<JiraIngestionPayload> currentPageContent, String nextPageToken) {}
    private final JiraPayloadParser parser;
    private final JiraRestClient jiraRestClient;

    public Stream<JiraIngestionPayload> getIssues(String taskId, JiraSyncRequest request) {
        log.info("Task [{}] - Starting to fetch JIRA issues. Request: {}", taskId, request);
        try {
            final String jql = buildJql(request);
            log.info("Task [{}] - Constructed JQL query: {}", taskId, jql);

            SearchResult initialResult = searchIssues(taskId, jql, null, request.getFields());
            log.info("Task [{}] - Initial search complete.", taskId);

            JiraPagedState initialState = new JiraPagedState(
                    parser.parse(initialResult.getIssues()),
                    initialResult.getNextPageToken()
            );

            return Stream.iterate(
                            initialState,
                            state -> !state.currentPageContent().isEmpty(),
                            currentState -> {
                                String nextToken = currentState.nextPageToken();
                                if (StringUtils.isEmpty(nextToken)) {
                                    log.info("Task [{}] - Reached the last page, no more nextPageToken.", taskId);
                                    return new JiraPagedState(Collections.emptyList(), null);
                                }
                                log.debug("Task [{}] - Fetching next page of data. Token: {}", taskId, nextToken);
                                SearchResult nextResult = searchIssues(taskId, jql, nextToken, request.getFields());
                                return new JiraPagedState(
                                        parser.parse(nextResult.getIssues()),
                                        nextResult.getNextPageToken()
                                );
                            }
                    )
                    .flatMap(state -> state.currentPageContent().stream());
        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch JIRA Issues.", taskId, e);
            throw new RuntimeException("Failed to fetch JIRA Issues", e);
        }
    }

    private SearchResult searchIssues(String taskId, String jql, String nextPageToken, Set<String> fields) {
        try {
            log.debug("Task [{}] - Executing JQL search. PageToken: '{}', Fields: {}", taskId, nextPageToken, fields);
            var searchClient = jiraRestClient.getSearchClient();

            var searchResultPromise = searchClient.enhancedSearchJql(jql, MAX_RESULTS_PER_PAGE, nextPageToken, fields, null);
            SearchResult result = searchResultPromise.get();
            log.debug("Task [{}] - JQL search successful.", taskId);
            return result;
        } catch (Exception e) {
            log.error("Task [{}] - JQL search execution failed. JQL: '{}', PageToken: '{}'", taskId, jql, nextPageToken, e);
            throw new RuntimeException("JQL search execution failed: " + jql, e);
        }
    }

    private String buildJql(JiraSyncRequest request) {
        log.debug("Building JQL. Request: {}", request);
        var jqlParts = new ArrayList<String>();

        // Project filter
        if (!CollectionUtils.isEmpty(request.getProjects())) {
            var projects = request.getProjects();
            if (!projects.contains("*")) {
                var projectFilter = projects.stream()
                        .map(key -> "\"" + key + "\"")
                        .collect(Collectors.joining(","));
                jqlParts.add("project in (" + projectFilter + ")");
            }
        }

        // Date range filter
        if (StringUtils.isNotBlank(request.getStartDate())) {
            jqlParts.add("updated >= \"" + request.getStartDate() + "\"");
        }

        if (StringUtils.isNotBlank(request.getEndDate())) {
            jqlParts.add("updated <= \"" + request.getEndDate() + "\"");
        }


        var jql = jqlParts.isEmpty() ? "" : String.join(" AND ", jqlParts);
        jql += " ORDER BY updated DESC";

        log.debug("Finished building JQL: {}", jql.trim());
        return jql.trim();
    }
}