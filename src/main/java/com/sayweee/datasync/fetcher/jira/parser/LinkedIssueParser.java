package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.atlassian.jira.rest.client.api.domain.IssueLink;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class LinkedIssueParser {

    /**
     * 解析 Issue 的关联问题信息
     *
     * @param issue JIRA Issue 对象
     * @return 关联问题实体列表
     */
    public List<LinkedIssueEntity> parse(Issue issue) {
        List<LinkedIssueEntity> linkedIssueEntities = new ArrayList<>();

        if (issue.getIssueLinks() == null) {
            return linkedIssueEntities;
        }

        for (IssueLink issueLink : issue.getIssueLinks()) {
            try {
                LinkedIssueEntity linkedIssueEntity = parseIssueLink(issueLink, issue);
                if (linkedIssueEntity != null) {
                    linkedIssueEntities.add(linkedIssueEntity);
                }
            } catch (Exception e) {
                log.warn("Failed to parse issue link for issue {}: {}",
                    issue.getKey(), e.getMessage(), e);
            }
        }

        return linkedIssueEntities;
    }

    /**
     * 解析单个 IssueLink
     *
     * @param issueLink JIRA IssueLink 对象
     * @param issue 当前 Issue 对象
     * @return LinkedIssueEntity 或 null
     */
    private LinkedIssueEntity parseIssueLink(IssueLink issueLink, Issue issue) {
        if (issueLink == null) {
            return null;
        }

        LinkedIssueEntity entity = new LinkedIssueEntity();

        // 设置 Issue ID
        if (issue.getId() != null) {
            entity.setIssueId(issue.getId().intValue());
        }

        // 设置故事键值 (当前 Issue 的 Key)
        entity.setStoryKey(issue.getKey());

        // 设置关联 Issue 的键值
        entity.setLinkKey(issueLink.getTargetIssueKey());

        // 设置关联类型
        if (issueLink.getIssueLinkType() != null) {
            entity.setLinkType(issueLink.getIssueLinkType().getName());
        }

        return entity;
    }
}
