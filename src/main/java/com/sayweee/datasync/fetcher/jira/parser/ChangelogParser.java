package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ChangelogParser {

    /**
     * 解析 JsonNode 格式的变更日志数据
     *
     * @param changelogData JsonNode 格式的变更日志数据，包含 issueId 和 changeHistories
     * @return 解析后的 ChangeLogEntity 列表
     */
    public List<ChangeLogEntity> parse(JsonNode changelogData) {
        List<ChangeLogEntity> changeLogEntities = new ArrayList<>();

        if (changelogData == null || !changelogData.has("changeHistories")) {
            return changeLogEntities;
        }

        Long issueId = changelogData.has("issueId") ? changelogData.get("issueId").asLong() : null;
        JsonNode changeHistories = changelogData.get("changeHistories");

        if (!changeHistories.isArray()) {
            return changeLogEntities;
        }

        for (JsonNode changeHistory : changeHistories) {
            try {
                List<ChangeLogEntity> historyEntities = parseChangeHistory(changeHistory, issueId);
                changeLogEntities.addAll(historyEntities);
            } catch (Exception e) {
                log.warn("Failed to parse change history for issue {}: {}", issueId, e.getMessage(), e);
            }
        }

        return changeLogEntities;
    }

    /**
     * 解析单个变更历史记录 (JsonNode 格式)
     */
    private List<ChangeLogEntity> parseChangeHistory(JsonNode changeHistory, Long issueId) {
        List<ChangeLogEntity> entities = new ArrayList<>();

        if (!changeHistory.has("items") || !changeHistory.get("items").isArray()) {
            return entities;
        }

        JsonNode items = changeHistory.get("items");
        for (JsonNode item : items) {
            try {
                ChangeLogEntity entity = parseChangeItem(item, changeHistory, issueId);
                entities.add(entity);
            } catch (Exception e) {
                String field = item.has("field") ? item.get("field").asText() : "unknown";
                log.warn("Failed to parse change item {} for issue {}: {}",
                        field, issueId, e.getMessage(), e);
            }
        }

        return entities;
    }

    /**
     * 解析单个变更项 (JsonNode 格式)
     */
    private ChangeLogEntity parseChangeItem(JsonNode item, JsonNode changeHistory, Long issueId) {
        ChangeLogEntity entity = new ChangeLogEntity();

        // 设置变更ID (从 changeHistory 的 id 字段获取)
        String changeId = changeHistory.has("id") ? changeHistory.get("id").asText() : null;
        if (changeId != null) {
            try {
                entity.setChangeId(Integer.parseInt(changeId));
            } catch (NumberFormatException e) {
                // 如果无法解析为整数，则生成一个哈希值
                entity.setChangeId(Math.abs(changeId.hashCode()));
            }
        }

        // 设置 Issue Key (从 issueId 生成，假设格式为 PROJ-123)
//        entity.setKey(issueId);
        entity.setIssueId(issueId);


        // 设置用户信息
        if (changeHistory.has("author")) {
            JsonNode author = changeHistory.get("author");
            if (author.has("accountId")) {
                entity.setUserId(author.get("accountId").asText());
            }
            if (author.has("displayName")) {
                entity.setUsername(author.get("displayName").asText());
            }
        }

        // 设置变更时间
        if (changeHistory.has("created")) {
            long timestamp = changeHistory.get("created").asLong();
            entity.setCreated(OffsetDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.UTC));
        }

        // 设置字段信息
        if (item.has("field")) {
            entity.setField(item.get("field").asText());
        }
        if (item.has("fromString")) {
            entity.setFromString(item.get("fromString").asText());
        }
        if (item.has("toString")) {
            entity.setToString(item.get("toString").asText());
        }
        // 注意：JsonNode 格式中可能没有 fromValue 和 toValue，设置为 null
        entity.setFromValue(null);
        entity.setToValue(null);

        // 设置入库时间
        entity.setInDate(LocalDateTime.now());

        return entity;
    }
}
