package com.sayweee.datasync.service;

import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.service.jira.IssueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 异步数据同步服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncSyncService {

    private final IssueService issueService;

    /**
     * 提交Jira数据同步任务
     */
    public String submitJiraSync(JiraSyncRequest request) {
        var taskId = generateTaskId("jira");
        log.info("提交Jira同步任务: {}", taskId);
        executeJiraSync(taskId, request);
        return taskId;
    }

    /**
     * 异步执行Jira数据同步
     */
    @Async("syncTaskExecutor")
    public void executeJiraSync(String taskId, JiraSyncRequest request) {
        log.info("开始执行Jira同步任务: {}", taskId);

        try {
            issueService.syncIssues(taskId, request);
        } catch (Exception e) {
            log.error("Jira同步任务失败: {}", taskId, e);
        }
    }

    /**
     * 生成任务ID
     * 格式: {type}-{yyyyMMdd}-{HHmmss}-{随机数}
     */
    private String generateTaskId(String type) {
        var now = java.time.LocalDateTime.now();
        var dateStr = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
        var timeStr = now.format(java.time.format.DateTimeFormatter.ofPattern("HHmmss"));
        var random = (int) (Math.random() * 1000);

        return String.format("%s-%s-%s-%03d", type, dateStr, timeStr, random);
    }
}