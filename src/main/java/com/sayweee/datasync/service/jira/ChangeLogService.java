package com.sayweee.datasync.service.jira;

import com.sayweee.datasync.fetcher.jira.ChangelogFetcher;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import com.sayweee.datasync.writer.jira.ChangeLogWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChangeLogService {
    private static final String BINDING_NAME = "jira-changelog-events-out-0";
    private final ChangelogFetcher changelogFetcher;
    private final ChangeLogWriter changeLogWriter;
    private final StreamBridge streamBridge;

    public void processAndFetchChangelog(List<JiraIngestionPayload> events) {
        changelogFetcher.fetch("changelog-fetcher", events).forEach(event -> {
            streamBridge.send(BINDING_NAME, event);
        });
    }

    public void processAndSaveChangelog(List<ChangeLogEntity> events) {
        if (events == null || events.isEmpty()) {
            log.info("No changelog events to process");
            return;
        }

        try {
            changeLogWriter.writeChangeLogs(events);
            log.info("Successfully processed {} changelog events", events.size());
        } catch (Exception e) {
            log.error("Failed to process changelog events", e);
            throw e;
        }
    }
}
