package com.sayweee.datasync.service.jira;

import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.RemoteLinkEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class RemoteLinkService {
    private final StreamBridge streamBridge;
    public void processAndFetchRemoteLink(List<JiraIngestionPayload> events) {

    }

    public void processAndSaveRemoteLink(List<RemoteLinkEntity> events) {

    }
}
