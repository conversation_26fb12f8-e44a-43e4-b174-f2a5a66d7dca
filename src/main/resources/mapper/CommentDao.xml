<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.CommentDao">

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
        id,
        issueid,
        userid,
        username,
        created,
        updated,
        in_date,
        comment
        ) VALUES
        <foreach collection="comments" item="item" separator=",">
            (
            #{item.id},
            #{item.issueId},
            #{item.userid},
            #{item.username},
            #{item.created},
            #{item.updated},
            SYSDATE,
            #{item.comment}
            )
        </foreach>
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
        WHERE id IN (SELECT id FROM ${tempTable})
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            id,
            issueid,
            userid,
            username,
            created,
            updated,
            in_date,
            comment
        )
        SELECT
            id,
            issueid,
            userid,
            username,
            created,
            updated,
            in_date,
            comment
        FROM ${tempTable}
    </insert>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>