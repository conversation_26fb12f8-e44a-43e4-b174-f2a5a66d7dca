<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.IssueTempDao">

    <insert id="batchInsert">
        INSERT INTO ${tableName} (
            id,
            summary,
            status,
            priority,
            issuetype,
            created,
            projectId,
            project_name,
            projectkey,
            key,
            issue_source,
            issue_module,
            issue_sub_module,
            fix_version_date,
            fix_version,
            due_date,
            story_points,
            resolution,
            resolved,
            issue_userview_category,
            issue_affects_platform,
            updated,
            in_date,
            purpose,
            rfq_plan_date,
            rfq_date,
            plan_release_date,
            reporter,
            developer,
            tester,
            original_estimate,
            smoke_check,
            legacy_issue,
            qa_team,
            work_category,
            parent_key,
            rfq_delay_comment,
            release_delay_comment,
            assignee,
            additional_requirements,
            parent_id,
            timespent,
            eta_shanghai,
            remark,
            components,
            bug_description_check
        ) VALUES
        <foreach collection="payloads" item="item" separator=",">
            (
                #{item.id},
                #{item.summary},
                #{item.status},
                #{item.priority},
                #{item.issuetype},
                #{item.created},
                #{item.projectId},
                #{item.projectName},
                #{item.projectKey},
                #{item.issueKey},
                #{item.issueSource},
                #{item.issueModule},
                #{item.issueSubModule},
                #{item.fixVersionDate},
                #{item.fixVersion},
                #{item.dueDate},
                #{item.storyPoints},
                #{item.resolution},
                #{item.resolved},
                #{item.issueUserviewCategory},
                #{item.issueAffectsPlatform},
                #{item.updated},
                #{item.inDate},
                #{item.purpose},
                #{item.rfqPlanDate},
                #{item.rfqDate},
                #{item.planReleaseDate},
                #{item.reporter},
                #{item.developer},
                #{item.tester},
                #{item.originalEstimate},
                #{item.smokeCheck},
                #{item.legacyIssue},
                #{item.qaTeam},
                #{item.workCategory},
                #{item.parentKey},
                #{item.rfqDelayComment},
                #{item.releaseDelayComment},
                #{item.assignee},
                #{item.additionalRequirements},
                #{item.parentId},
                #{item.timeSpent},
                #{item.etaShanghai},
                #{item.remark},
                #{item.components},
                #{item.bugDescriptionCheck}
            )
        </foreach>
    </insert>

    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
            USING ${tempTable}
        WHERE ${targetTable}.id = ${tempTable}.id
    </delete>

    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            id,
            summary,
            status,
            priority,
            issuetype,
            created,
            projectId,
            project_name,
            projectkey,
            key,
            issue_source,
            issue_module,
            issue_sub_module,
            fix_version_date,
            fix_version,
            due_date,
            story_points,
            resolution,
            resolved,
            issue_userview_category,
            issue_affects_platform,
            updated,
            in_date,
            purpose,
            rfq_plan_date,
            rfq_date,
            plan_release_date,
            reporter,
            developer,
            tester,
            original_estimate,
            smoke_check,
            legacy_issue,
            qa_team,
            work_category,
            parent_key,
            rfq_delay_comment,
            release_delay_comment,
            assignee,
            additional_requirements,
            parent_id,
            timespent,
            eta_shanghai,
            remark,
            components,
            bug_description_check
        )
        SELECT
            id,
            summary,
            status,
            priority,
            issuetype,
            created,
            projectid,
            project_name,
            projectkey,
            key,
            issue_source,
            issue_module,
            issue_sub_module,
            fix_version_date,
            fix_version,
            due_date,
            story_points,
            resolution,
            resolved,
            issue_userview_category,
            issue_affects_platform,
            updated,
            in_date,
            purpose,
            rfq_plan_date,
            rfq_date,
            plan_release_date,
            reporter,
            developer,
            tester,
            original_estimate,
            smoke_check,
            legacy_issue,
            qa_team,
            work_category,
            parent_key,
            rfq_delay_comment,
            release_delay_comment,
            assignee,
            additional_requirements,
            parent_id,
            timespent,
            eta_shanghai,
            remark,
            components,
            bug_description_check
        FROM ${tempTable}
    </insert>

    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 优化版本：分批批量插入，避免单次插入过多数据 -->
    <insert id="batchInsertChunked" parameterType="map">
        INSERT INTO ${tableName} (
        id,
        summary,
        status,
        priority,
        issuetype,
        created,
        projectId,
        project_name,
        projectkey,
        key,
        issue_source,
        issue_module,
        issue_sub_module,
        fix_version_date,
        fix_version,
        due_date,
        story_points,
        resolution,
        resolved,
        issue_userview_category,
        issue_affects_platform,
        updated,
        in_date,
        purpose,
        rfq_plan_date,
        rfq_date,
        plan_release_date,
        reporter,
        developer,
        tester,
        original_estimate,
        smoke_check,
        legacy_issue,
        qa_team,
        work_category,
        parent_key,
        rfq_delay_comment,
        release_delay_comment,
        assignee,
        additional_requirements,
        parent_id,
        timespent,
        eta_shanghai,
        remark,
        components,
        bug_description_check
        ) VALUES
        <foreach collection="payloads" item="item" separator=",">
            (
                #{item.id,jdbcType=BIGINT},
                #{item.summary,jdbcType=VARCHAR},
                #{item.status,jdbcType=VARCHAR},
                #{item.priority,jdbcType=VARCHAR},
                #{item.issuetype,jdbcType=VARCHAR},
                #{item.created,jdbcType=TIMESTAMP},
                #{item.projectId,jdbcType=VARCHAR},
                #{item.projectName,jdbcType=VARCHAR},
                #{item.projectKey,jdbcType=VARCHAR},
                #{item.issueKey,jdbcType=VARCHAR},
                #{item.issueSource,jdbcType=VARCHAR},
                #{item.issueModule,jdbcType=VARCHAR},
                #{item.issueSubModule,jdbcType=VARCHAR},
                #{item.fixVersionDate,jdbcType=TIMESTAMP},
                #{item.fixVersion,jdbcType=VARCHAR},
                #{item.dueDate,jdbcType=DATE},
                #{item.storyPoints,jdbcType=DECIMAL},
                #{item.resolution,jdbcType=VARCHAR},
                #{item.resolved,jdbcType=TIMESTAMP},
                #{item.issueUserviewCategory,jdbcType=VARCHAR},
                #{item.issueAffectsPlatform,jdbcType=VARCHAR},
                #{item.updated,jdbcType=TIMESTAMP},
                #{item.inDate,jdbcType=TIMESTAMP},
                #{item.purpose,jdbcType=VARCHAR},
                #{item.rfqPlanDate,jdbcType=TIMESTAMP},
                #{item.rfqDate,jdbcType=TIMESTAMP},
                #{item.planReleaseDate,jdbcType=DATE},
                #{item.reporter,jdbcType=VARCHAR},
                #{item.developer,jdbcType=VARCHAR},
                #{item.tester,jdbcType=VARCHAR},
                #{item.originalEstimate,jdbcType=INTEGER},
                #{item.smokeCheck,jdbcType=VARCHAR},
                #{item.legacyIssue,jdbcType=VARCHAR},
                #{item.qaTeam,jdbcType=VARCHAR},
                #{item.workCategory,jdbcType=VARCHAR},
                #{item.parentKey,jdbcType=VARCHAR},
                #{item.rfqDelayComment,jdbcType=VARCHAR},
                #{item.releaseDelayComment,jdbcType=VARCHAR},
                #{item.assignee,jdbcType=VARCHAR},
                #{item.additionalRequirements,jdbcType=VARCHAR},
                #{item.parentId,jdbcType=INTEGER},
                #{item.timeSpent,jdbcType=INTEGER},
                #{item.etaShanghai,jdbcType=DATE},
                #{item.remark,jdbcType=VARCHAR},
                #{item.components,jdbcType=VARCHAR},
                #{item.bugDescriptionCheck,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- UPSERT 操作：使用 MERGE 语句（如果数据库支持） -->
    <update id="mergeFromTempToTarget">
        MERGE INTO ${targetTable} AS target
        USING ${tempTable} AS source
        ON target.id = source.id
        WHEN MATCHED THEN
            UPDATE SET
                summary = source.summary,
                status = source.status,
                priority = source.priority,
                issuetype = source.issuetype,
                created = source.created,
                projectId = source.projectid,
                project_name = source.project_name,
                projectkey = source.projectkey,
                key = source.key,
                issue_source = source.issue_source,
                issue_module = source.issue_module,
                issue_sub_module = source.issue_sub_module,
                fix_version_date = source.fix_version_date,
                fix_version = source.fix_version,
                due_date = source.due_date,
                story_points = source.story_points,
                resolution = source.resolution,
                resolved = source.resolved,
                issue_userview_category = source.issue_userview_category,
                issue_affects_platform = source.issue_affects_platform,
                updated = source.updated,
                in_date = source.in_date,
                purpose = source.purpose,
                rfq_plan_date = source.rfq_plan_date,
                rfq_date = source.rfq_date,
                plan_release_date = source.plan_release_date,
                reporter = source.reporter,
                developer = source.developer,
                tester = source.tester,
                original_estimate = source.original_estimate,
                smoke_check = source.smoke_check,
                legacy_issue = source.legacy_issue,
                qa_team = source.qa_team,
                work_category = source.work_category,
                parent_key = source.parent_key,
                rfq_delay_comment = source.rfq_delay_comment,
                release_delay_comment = source.release_delay_comment,
                assignee = source.assignee,
                additional_requirements = source.additional_requirements,
                parent_id = source.parent_id,
                timespent = source.timespent,
                eta_shanghai = source.eta_shanghai,
                remark = source.remark,
                components = source.components,
                bug_description_check = source.bug_description_check
        WHEN NOT MATCHED THEN
            INSERT (
                id,
                summary,
                status,
                priority,
                issuetype,
                created,
                projectId,
                project_name,
                projectkey,
                key,
                issue_source,
                issue_module,
                issue_sub_module,
                fix_version_date,
                fix_version,
                due_date,
                story_points,
                resolution,
                resolved,
                issue_userview_category,
                issue_affects_platform,
                updated,
                in_date,
                purpose,
                rfq_plan_date,
                rfq_date,
                plan_release_date,
                reporter,
                developer,
                tester,
                original_estimate,
                smoke_check,
                legacy_issue,
                qa_team,
                work_category,
                parent_key,
                rfq_delay_comment,
                release_delay_comment,
                assignee,
                additional_requirements,
                parent_id,
                timespent,
                eta_shanghai,
                remark,
                components,
                bug_description_check
            ) VALUES (
                source.id, source.summary, source.status, source.priority, source.issuetype, source.created,
                source.projectid, source.project_name, source.projectkey, source.key, source.issue_source,
                source.issue_module, source.issue_sub_module, source.fix_version_date, source.fix_version,
                source.due_date, source.story_points, source.resolution, source.resolved, source.issue_userview_category,
                source.issue_affects_platform, source.updated, source.in_date, source.purpose, source.rfq_plan_date,
                source.rfq_date, source.plan_release_date, source.reporter, source.developer, source.tester,
                source.original_estimate, source.smoke_check, source.legacy_issue, source.qa_team, source.work_category,
                source.parent_key, source.rfq_delay_comment, source.release_delay_comment, source.assignee,
                source.additional_requirements, source.parent_id, source.timespent, source.eta_shanghai,
                source.remark, source.components, source.bug_description_check
            )
    </update>

</mapper>