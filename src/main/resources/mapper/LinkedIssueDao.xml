<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sayweee.datasync.dao.LinkedIssueDao">

    <!-- 创建临时表 -->
    <update id="createTempTableLike">
        CREATE TABLE ${tempTable} as select * from ${targetTable} where 1=0
    </update>

    <!-- 批量插入到临时表 -->
    <insert id="batchInsert">
        INSERT INTO ${tableName} (
            issue_id,
            story_key,
            link_key,
            link_type
        ) VALUES
        <foreach collection="linkedIssues" item="item" separator=",">
            (
                #{item.issueId},
                #{item.storyKey},
                #{item.linkKey},
                #{item.linkType}
            )
        </foreach>
    </insert>

    <!-- 优化版本：分批批量插入 -->
    <insert id="batchInsertChunked" parameterType="map">
        INSERT INTO ${tableName} (
            issue_id,
            story_key,
            link_key,
            link_type
        ) VALUES
        <foreach collection="linkedIssues" item="item" separator=",">
            (
                #{item.issueId},
                #{item.storyKey},
                #{item.linkKey},
                #{item.linkType}
            )
        </foreach>
    </insert>

    <!-- 从目标表删除在临时表中存在的记录 -->
    <delete id="deleteFromTargetUsingTemp">
        DELETE FROM ${targetTable}
            USING ${tempTable}
        WHERE ${targetTable}.issue_id = ${tempTable}.issue_id
          AND ${targetTable}.story_key = ${tempTable}.story_key
          AND ${targetTable}.link_key = ${tempTable}.link_key
    </delete>

    <!-- 从临时表插入到目标表 -->
    <insert id="insertFromTempToTarget">
        INSERT INTO ${targetTable} (
            issue_id,
            story_key,
            link_key,
            link_type
        )
        SELECT
            issue_id,
            story_key,
            link_key,
            link_type
        FROM ${tempTable}
    </insert>

    <!-- MERGE操作（如果数据库支持） -->
    <update id="mergeFromTempToTarget">
        MERGE INTO ${targetTable} AS target
        USING ${tempTable} AS source
        ON (target.issue_id = source.issue_id 
            AND target.story_key = source.story_key 
            AND target.link_key = source.link_key)
        WHEN MATCHED THEN
            UPDATE SET
                link_type = source.link_type
        WHEN NOT MATCHED THEN
            INSERT (
                issue_id,
                story_key,
                link_key,
                link_type
            )
            VALUES (
                source.issue_id,
                source.story_key,
                source.link_key,
                source.link_type
            )
    </update>

    <!-- 删除表 -->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

</mapper>
