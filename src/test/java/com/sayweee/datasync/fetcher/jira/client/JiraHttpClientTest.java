package com.sayweee.datasync.fetcher.jira.client;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JiraHttpClientTest {

    @Mock
    private HttpClient httpClient;

    @Mock
    private HttpResponse<String> httpResponse;

    @InjectMocks
    private JiraHttpClient jiraHttpClient;

    @BeforeEach
    void setUp() {
        // 设置测试用的配置值
        ReflectionTestUtils.setField(jiraHttpClient, "JIRA_SERVER_URI", URI.create("https://test.atlassian.net"));
        ReflectionTestUtils.setField(jiraHttpClient, "JIRA_EMAIL", "<EMAIL>");
        ReflectionTestUtils.setField(jiraHttpClient, "JIRA_API_TOKEN", "test-token");
    }

    @Test
    void testSendPostRequestSuccess() throws IOException, InterruptedException {
        // Given
        String taskId = "test-task";
        String endpoint = "/rest/api/3/test";
        String jsonPayload = "{\"test\": \"data\"}";
        String responseBody = "{\"result\": \"success\"}";

        when(httpResponse.statusCode()).thenReturn(200);
        when(httpResponse.body()).thenReturn(responseBody);
        when(httpClient.send(any(HttpRequest.class), eq(HttpResponse.BodyHandlers.ofString())))
                .thenReturn(httpResponse);

        // When
        JsonNode result = jiraHttpClient.sendPostRequest(taskId, endpoint, jsonPayload);

        // Then
        assertNotNull(result);
        assertTrue(result.has("result"));
        assertEquals("success", result.get("result").asText());

        // 验证 HTTP 请求被正确发送
        verify(httpClient).send(any(HttpRequest.class), eq(HttpResponse.BodyHandlers.ofString()));
    }

    @Test
    void testSendPostRequestFailure() throws IOException, InterruptedException {
        // Given
        String taskId = "test-task";
        String endpoint = "/rest/api/3/test";
        String jsonPayload = "{\"test\": \"data\"}";

        when(httpResponse.statusCode()).thenReturn(400);
        when(httpResponse.body()).thenReturn("Bad Request");
        when(httpClient.send(any(HttpRequest.class), eq(HttpResponse.BodyHandlers.ofString())))
                .thenReturn(httpResponse);

        // When & Then
        IOException exception = assertThrows(IOException.class, () -> {
            jiraHttpClient.sendPostRequest(taskId, endpoint, jsonPayload);
        });

        assertTrue(exception.getMessage().contains("Unexpected response code: 400"));
        assertTrue(exception.getMessage().contains("Bad Request"));
    }

    @Test
    void testSendGetRequestSuccess() throws IOException, InterruptedException {
        // Given
        String taskId = "test-task";
        String endpoint = "/rest/api/3/test";
        String responseBody = "{\"result\": \"success\"}";

        when(httpResponse.statusCode()).thenReturn(200);
        when(httpResponse.body()).thenReturn(responseBody);
        when(httpClient.send(any(HttpRequest.class), eq(HttpResponse.BodyHandlers.ofString())))
                .thenReturn(httpResponse);

        // When
        JsonNode result = jiraHttpClient.sendGetRequest(taskId, endpoint);

        // Then
        assertNotNull(result);
        assertTrue(result.has("result"));
        assertEquals("success", result.get("result").asText());
    }

    @Test
    void testGetJiraServerUri() {
        // When
        URI uri = jiraHttpClient.getJiraServerUri();

        // Then
        assertNotNull(uri);
        assertEquals("https://test.atlassian.net", uri.toString());
    }

    @Test
    void testCreateAuthenticatedRequestBuilder() {
        // Given
        URI testUri = URI.create("https://test.atlassian.net/rest/api/3/test");

        // When
        HttpRequest.Builder builder = jiraHttpClient.createAuthenticatedRequestBuilder(testUri);

        // Then
        assertNotNull(builder);
        
        // 构建请求以验证头部设置
        HttpRequest request = builder.GET().build();
        assertEquals(testUri, request.uri());
        assertTrue(request.headers().firstValue("Authorization").isPresent());
        assertTrue(request.headers().firstValue("Accept").isPresent());
        assertEquals("application/json", request.headers().firstValue("Accept").get());
    }
}
