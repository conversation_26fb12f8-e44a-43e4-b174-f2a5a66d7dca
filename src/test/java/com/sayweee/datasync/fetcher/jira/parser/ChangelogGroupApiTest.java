package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;

/**
 * 探索 ChangelogGroup 的可用方法
 */
public class ChangelogGroupApiTest {
    
    @Test
    void exploreChangelogGroupMethods() {
        Class<ChangelogGroup> clazz = ChangelogGroup.class;
        Method[] methods = clazz.getMethods();
        
        System.out.println("ChangelogGroup 可用方法:");
        for (Method method : methods) {
            if (method.getDeclaringClass() == clazz || 
                method.getName().startsWith("get") || 
                method.getName().startsWith("is")) {
                System.out.println("- " + method.getName() + "(): " + method.getReturnType().getSimpleName());
            }
        }
    }
}
