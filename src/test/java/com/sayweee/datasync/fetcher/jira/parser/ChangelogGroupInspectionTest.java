package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 检查 ChangelogGroup 的内部结构
 */
public class ChangelogGroupInspectionTest {
    
    @Test
    void inspectChangelogGroupStructure() {
        Class<ChangelogGroup> clazz = ChangelogGroup.class;
        
        System.out.println("=== ChangelogGroup 字段 ===");
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            System.out.println("Field: " + field.getName() + " - " + field.getType().getSimpleName());
        }
        
        System.out.println("\n=== ChangelogGroup 方法 ===");
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getParameterCount() == 0 && 
                !method.getName().equals("getClass") &&
                !method.getName().equals("hashCode") &&
                !method.getName().equals("toString") &&
                !method.getName().equals("notify") &&
                !method.getName().equals("notifyAll") &&
                !method.getName().equals("wait")) {
                System.out.println("Method: " + method.getName() + "() -> " + method.getReturnType().getSimpleName());
            }
        }
        
        System.out.println("\n=== 查找包含 'id' 的方法 ===");
        for (Method method : methods) {
            if (method.getName().toLowerCase().contains("id") && method.getParameterCount() == 0) {
                System.out.println("ID Method: " + method.getName() + "() -> " + method.getReturnType().getSimpleName());
            }
        }
    }
}
