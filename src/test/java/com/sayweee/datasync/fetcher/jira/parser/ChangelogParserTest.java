package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.*;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChangelogParserTest {

    @InjectMocks
    private ChangelogParser changelogParser;

    private Issue mockIssue;
    private ChangelogGroup mockChangelogGroup;
    private ChangelogItem mockChangelogItem1;
    private ChangelogItem mockChangelogItem2;
    private BasicUser mockUser;
    private BasicProject mockProject;

    @BeforeEach
    void setUp() {
        mockIssue = mock(Issue.class);
        mockChangelogGroup = mock(ChangelogGroup.class);
        mockChangelogItem1 = mock(ChangelogItem.class);
        mockChangelogItem2 = mock(ChangelogItem.class);
        mockUser = mock(BasicUser.class);
        mockProject = mock(BasicProject.class);

        // 设置 Issue
        when(mockIssue.getKey()).thenReturn("TEST-123");
        when(mockIssue.getProject()).thenReturn(mockProject);

        // 设置 Project
        when(mockProject.getId()).thenReturn(10001L);

        // 设置用户
        when(mockUser.getAccountId()).thenReturn("user123");
        when(mockUser.getDisplayName()).thenReturn("John Doe");

        // 设置 ChangelogGroup
        when(mockChangelogGroup.getAuthor()).thenReturn(mockUser);
        when(mockChangelogGroup.getCreated()).thenReturn(DateTime.now());

        // 设置 ChangelogItem1 - 状态变更
        when(mockChangelogItem1.getField()).thenReturn("status");
        when(mockChangelogItem1.getFrom()).thenReturn("1");
        when(mockChangelogItem1.getFromString()).thenReturn("Open");
        when(mockChangelogItem1.getTo()).thenReturn("3");
        when(mockChangelogItem1.getToString()).thenReturn("In Progress");

        // 设置 ChangelogItem2 - 经办人变更
        when(mockChangelogItem2.getField()).thenReturn("assignee");
        when(mockChangelogItem2.getFrom()).thenReturn("user456");
        when(mockChangelogItem2.getFromString()).thenReturn("Jane Smith");
        when(mockChangelogItem2.getTo()).thenReturn("user789");
        when(mockChangelogItem2.getToString()).thenReturn("Bob Wilson");
    }

    @Test
    void testParseWithChangelog() {
        // Given
        when(mockChangelogGroup.getItems()).thenReturn(Arrays.asList(mockChangelogItem1, mockChangelogItem2));
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList(mockChangelogGroup));

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个变更记录 (状态变更)
        ChangeLogEntity change1 = result.get(0);
        assertNotNull(change1.getChangeId()); // changeId 是生成的哈希值
        assertEquals("TEST-123", change1.getKey());
        assertEquals(10001, change1.getProjectId());
        assertEquals("user123", change1.getUserId());
        assertEquals("John Doe", change1.getUsername());
        assertEquals("status", change1.getField());
        assertEquals("1", change1.getFromValue());
        assertEquals("Open", change1.getFromString());
        assertEquals("3", change1.getToValue());
        assertEquals("In Progress", change1.getToString());
        assertNotNull(change1.getCreated());
        assertNotNull(change1.getInDate());

        // 验证第二个变更记录 (经办人变更)
        ChangeLogEntity change2 = result.get(1);
        assertNotNull(change2.getChangeId()); // changeId 是生成的哈希值
        assertEquals("TEST-123", change2.getKey());
        assertEquals(10001, change2.getProjectId());
        assertEquals("user123", change2.getUserId());
        assertEquals("John Doe", change2.getUsername());
        assertEquals("assignee", change2.getField());
        assertEquals("user456", change2.getFromValue());
        assertEquals("Jane Smith", change2.getFromString());
        assertEquals("user789", change2.getToValue());
        assertEquals("Bob Wilson", change2.getToString());
        assertNotNull(change2.getCreated());
        assertNotNull(change2.getInDate());

        // 验证两个变更记录的 changeId 不同（因为字段不同）
        assertNotEquals(change1.getChangeId(), change2.getChangeId());
    }

    @Test
    void testParseWithNoChangelog() {
        // Given
        when(mockIssue.getChangelog()).thenReturn(null);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithEmptyChangelog() {
        // Given
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList());

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithNullChangelogItems() {
        // Given
        when(mockChangelogGroup.getItems()).thenReturn(null);
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList(mockChangelogGroup));

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithNullAuthor() {
        // Given
        when(mockChangelogGroup.getAuthor()).thenReturn(null);
        when(mockChangelogGroup.getItems()).thenReturn(Arrays.asList(mockChangelogItem1));
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList(mockChangelogGroup));

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ChangeLogEntity change = result.get(0);
        assertNull(change.getUserId());
        assertNull(change.getUsername());
    }

    @Test
    void testParseWithNullProject() {
        // Given
        when(mockIssue.getProject()).thenReturn(null);
        when(mockChangelogGroup.getItems()).thenReturn(Arrays.asList(mockChangelogItem1));
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList(mockChangelogGroup));

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ChangeLogEntity change = result.get(0);
        assertNull(change.getProjectId());
    }

    @Test
    void testParseWithNullCreatedTime() {
        // Given
        when(mockChangelogGroup.getCreated()).thenReturn(null);
        when(mockChangelogGroup.getItems()).thenReturn(Arrays.asList(mockChangelogItem1));
        when(mockIssue.getChangelog()).thenReturn(Arrays.asList(mockChangelogGroup));

        // When
        List<ChangeLogEntity> result = changelogParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ChangeLogEntity change = result.get(0);
        assertNotNull(change.getChangeId()); // 即使时间为null，也会生成changeId
        assertNull(change.getCreated());
    }
}
