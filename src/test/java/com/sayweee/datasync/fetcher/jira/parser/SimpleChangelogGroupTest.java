package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 简单的 ChangelogGroup 结构检查
 */
public class SimpleChangelogGroupTest {
    
    @Test
    void inspectChangelogGroup() {
        Class<ChangelogGroup> clazz = ChangelogGroup.class;
        
        System.out.println("=== ChangelogGroup 构造函数 ===");
        Constructor<?>[] constructors = clazz.getConstructors();
        for (Constructor<?> constructor : constructors) {
            System.out.print("Constructor: " + constructor.getName() + "(");
            Class<?>[] paramTypes = constructor.getParameterTypes();
            for (int i = 0; i < paramTypes.length; i++) {
                System.out.print(paramTypes[i].getSimpleName());
                if (i < paramTypes.length - 1) {
                    System.out.print(", ");
                }
            }
            System.out.println(")");
        }
        
        System.out.println("\n=== ChangelogGroup 字段 ===");
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            System.out.println("Field: " + field.getName() + " - " + field.getType().getSimpleName());
        }
        
        System.out.println("\n=== ChangelogGroup getter 方法 ===");
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getParameterCount() == 0 && 
                method.getName().startsWith("get") &&
                !method.getName().equals("getClass")) {
                System.out.println("Method: " + method.getName() + "() -> " + method.getReturnType().getSimpleName());
            }
        }
    }
}
