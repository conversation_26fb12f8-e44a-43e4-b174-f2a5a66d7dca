package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.ChangelogGroup;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;

import static org.mockito.Mockito.*;

/**
 * 测试 ChangelogGroup 的 ID 获取方法
 */
public class ChangelogIdTest {
    
    @Test
    void testChangelogGroupMethods() {
        ChangelogGroup mockGroup = mock(ChangelogGroup.class);
        
        // 打印所有可用方法
        Method[] methods = ChangelogGroup.class.getMethods();
        System.out.println("ChangelogGroup 可用方法:");
        for (Method method : methods) {
            if (method.getParameterCount() == 0 && 
                (method.getName().toLowerCase().contains("id") || 
                 method.getName().startsWith("get") ||
                 method.getName().startsWith("is"))) {
                System.out.println("- " + method.getName() + "(): " + method.getReturnType().getSimpleName());
            }
        }
    }
    
    @Test
    void testReflectionIdAccess() {
        ChangelogGroup mockGroup = mock(ChangelogGroup.class);
        
        // 尝试不同的方法名
        String[] methodNames = {"getId", "id", "getHistoryId", "getChangelogId"};
        
        for (String methodName : methodNames) {
            try {
                Method method = ChangelogGroup.class.getMethod(methodName);
                System.out.println("Found method: " + methodName + "() -> " + method.getReturnType());
            } catch (NoSuchMethodException e) {
                System.out.println("Method not found: " + methodName + "()");
            }
        }
    }
}
