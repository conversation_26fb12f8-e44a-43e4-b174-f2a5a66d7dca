package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.BasicUser;
import com.atlassian.jira.rest.client.api.domain.Comment;
import com.atlassian.jira.rest.client.api.domain.Issue;
import com.sayweee.datasync.model.entity.CommentEntity;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.net.URI;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CommentParserTest {

    @InjectMocks
    private CommentParser commentParser;

    private Issue mockIssue;
    private Comment mockComment1;
    private Comment mockComment2;
    private BasicUser mockUser;

    @BeforeEach
    void setUp() throws Exception {
        mockIssue = mock(Issue.class);
        mockComment1 = mock(Comment.class);
        mockComment2 = mock(Comment.class);
        mockUser = mock(BasicUser.class);

        // 设置 Issue
        when(mockIssue.getId()).thenReturn(12345L);
        when(mockIssue.getKey()).thenReturn("TEST-123");

        // 设置用户
        when(mockUser.getAccountId()).thenReturn("user123");
        when(mockUser.getDisplayName()).thenReturn("John Doe");

        // 设置评论1 - JSON格式
        when(mockComment1.getId()).thenReturn(67890L);
        when(mockComment1.getAuthor()).thenReturn(mockUser);
        when(mockComment1.getCreationDate()).thenReturn(DateTime.now());
        when(mockComment1.getUpdateDate()).thenReturn(DateTime.now());
        when(mockComment1.getSelf()).thenReturn(URI.create("https://jira.example.com/rest/api/2/issue/12345/comment/67890"));
        when(mockComment1.getBody()).thenReturn("{\"content\":[{\"content\":[{\"text\":\"This is a test comment\"}]}]}");

        // 设置评论2 - 纯文本格式
        when(mockComment2.getId()).thenReturn(67891L);
        when(mockComment2.getAuthor()).thenReturn(mockUser);
        when(mockComment2.getCreationDate()).thenReturn(DateTime.now());
        when(mockComment2.getUpdateDate()).thenReturn(DateTime.now());
        when(mockComment2.getSelf()).thenReturn(URI.create("https://jira.example.com/rest/api/2/issue/12345/comment/67891"));
        when(mockComment2.getBody()).thenReturn("This is a plain text comment");
    }

    @Test
    void testParseWithComments() {
        // Given
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1, mockComment2));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个评论 (JSON格式)
        CommentEntity comment1 = result.get(0);
        assertEquals(67890L, comment1.getId());
        assertEquals(12345L, comment1.getIssueId());
        assertEquals("user123", comment1.getUserid());
        assertEquals("John Doe", comment1.getUsername());
        assertEquals("This is a test comment", comment1.getComment());
        assertNotNull(comment1.getCreated());
        assertNotNull(comment1.getUpdated());

        // 验证第二个评论 (纯文本格式)
        CommentEntity comment2 = result.get(1);
        assertEquals(67891L, comment2.getId());
        assertEquals(12345L, comment2.getIssueId());
        assertEquals("user123", comment2.getUserid());
        assertEquals("John Doe", comment2.getUsername());
        assertEquals("This is a plain text comment", comment2.getComment());
        assertNotNull(comment2.getCreated());
        assertNotNull(comment2.getUpdated());
    }

    @Test
    void testParseWithNoComments() {
        // Given
        when(mockIssue.getComments()).thenReturn(null);

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithEmptyComments() {
        // Given
        when(mockIssue.getComments()).thenReturn(Arrays.asList());

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseCommentWithNullAuthor() {
        // Given
        when(mockComment1.getAuthor()).thenReturn(null);
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CommentEntity comment = result.get(0);
        assertNull(comment.getUserid());
        assertNull(comment.getUsername());
    }

    @Test
    void testParseCommentWithNullSelf() {
        // Given
        when(mockComment1.getSelf()).thenReturn(null);
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CommentEntity comment = result.get(0);
        assertEquals(12345L, comment.getIssueId()); // 应该使用 fallback issueId
    }

    @Test
    void testParseCommentWithComplexJson() {
        // Given
        String complexJson = "{\"content\":[" +
                "{\"content\":[{\"text\":\"First paragraph\"}]}," +
                "{\"content\":[{\"text\":\"Second paragraph\"}]}" +
                "]}";
        when(mockComment1.getBody()).thenReturn(complexJson);
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CommentEntity comment = result.get(0);
        assertEquals("First paragraph Second paragraph", comment.getComment());
    }

    @Test
    void testParseCommentWithEmptyBody() {
        // Given
        when(mockComment1.getBody()).thenReturn("");
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CommentEntity comment = result.get(0);
        assertNull(comment.getComment());
    }

    @Test
    void testParseCommentWithNullBody() {
        // Given
        when(mockComment1.getBody()).thenReturn(null);
        when(mockIssue.getComments()).thenReturn(Arrays.asList(mockComment1));

        // When
        List<CommentEntity> result = commentParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CommentEntity comment = result.get(0);
        assertNull(comment.getComment());
    }
}
