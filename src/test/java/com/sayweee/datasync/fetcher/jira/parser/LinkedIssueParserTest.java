package com.sayweee.datasync.fetcher.jira.parser;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.atlassian.jira.rest.client.api.domain.IssueLink;
import com.atlassian.jira.rest.client.api.domain.IssueLinkType;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LinkedIssueParserTest {

    @InjectMocks
    private LinkedIssueParser linkedIssueParser;

    private Issue mockIssue;
    private IssueLink mockIssueLink1;
    private IssueLink mockIssueLink2;
    private IssueLinkType mockLinkType1;
    private IssueLinkType mockLinkType2;

    @BeforeEach
    void setUp() {
        mockIssue = mock(Issue.class);
        mockIssueLink1 = mock(IssueLink.class);
        mockIssueLink2 = mock(IssueLink.class);
        mockLinkType1 = mock(IssueLinkType.class);
        mockLinkType2 = mock(IssueLinkType.class);

        // 设置 Issue
        when(mockIssue.getId()).thenReturn(12345L);
        when(mockIssue.getKey()).thenReturn("TEST-123");

        // 设置第一个 IssueLink
        when(mockIssueLink1.getTargetIssueKey()).thenReturn("TEST-456");
        when(mockIssueLink1.getIssueLinkType()).thenReturn(mockLinkType1);
        when(mockLinkType1.getName()).thenReturn("blocks");

        // 设置第二个 IssueLink
        when(mockIssueLink2.getTargetIssueKey()).thenReturn("TEST-789");
        when(mockIssueLink2.getIssueLinkType()).thenReturn(mockLinkType2);
        when(mockLinkType2.getName()).thenReturn("relates to");
    }

    @Test
    void testParseWithIssueLinks() {
        // Given
        when(mockIssue.getIssueLinks()).thenReturn(Arrays.asList(mockIssueLink1, mockIssueLink2));

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个关联问题
        LinkedIssueEntity link1 = result.get(0);
        assertEquals(12345, link1.getIssueId());
        assertEquals("TEST-123", link1.getStoryKey());
        assertEquals("TEST-456", link1.getLinkKey());
        assertEquals("blocks", link1.getLinkType());

        // 验证第二个关联问题
        LinkedIssueEntity link2 = result.get(1);
        assertEquals(12345, link2.getIssueId());
        assertEquals("TEST-123", link2.getStoryKey());
        assertEquals("TEST-789", link2.getLinkKey());
        assertEquals("relates to", link2.getLinkType());
    }

    @Test
    void testParseWithNoIssueLinks() {
        // Given
        when(mockIssue.getIssueLinks()).thenReturn(null);

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithEmptyIssueLinks() {
        // Given
        when(mockIssue.getIssueLinks()).thenReturn(Arrays.asList());

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseWithNullIssueId() {
        // Given
        when(mockIssue.getId()).thenReturn(null);
        when(mockIssue.getIssueLinks()).thenReturn(Arrays.asList(mockIssueLink1));

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        LinkedIssueEntity link = result.get(0);
        assertNull(link.getIssueId());
        assertEquals("TEST-123", link.getStoryKey());
        assertEquals("TEST-456", link.getLinkKey());
        assertEquals("blocks", link.getLinkType());
    }

    @Test
    void testParseWithNullLinkType() {
        // Given
        when(mockIssueLink1.getIssueLinkType()).thenReturn(null);
        when(mockIssue.getIssueLinks()).thenReturn(Arrays.asList(mockIssueLink1));

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        LinkedIssueEntity link = result.get(0);
        assertEquals(12345, link.getIssueId());
        assertEquals("TEST-123", link.getStoryKey());
        assertEquals("TEST-456", link.getLinkKey());
        assertNull(link.getLinkType());
    }

    @Test
    void testParseWithExceptionInLink() {
        // Given
        when(mockIssueLink1.getTargetIssueKey()).thenThrow(new RuntimeException("Test exception"));
        when(mockIssue.getIssueLinks()).thenReturn(Arrays.asList(mockIssueLink1, mockIssueLink2));

        // When
        List<LinkedIssueEntity> result = linkedIssueParser.parse(mockIssue);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // 只有第二个链接成功解析
        
        LinkedIssueEntity link = result.get(0);
        assertEquals("TEST-789", link.getLinkKey());
        assertEquals("relates to", link.getLinkType());
    }
}
