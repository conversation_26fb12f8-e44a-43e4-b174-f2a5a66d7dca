package com.sayweee.datasync.dao;

import com.sayweee.datasync.model.entity.IssueEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.ion.Decimal;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;

/**
 * IssueTempDao 测试类
 * 测试批量插入 SQL 的正确性和性能
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class IssueTempDaoTest {

    private IssueTempDao issueTempDao;
    
    private String testTempTableName;
    private String testTargetTableName;

    @BeforeEach
    void setUp() {
        testTempTableName = "temp_test_issues_" + System.currentTimeMillis();
        testTargetTableName = "test_jira_issues";
    }

    @Test
    void testBatchInsert() {
        // Given
        List<IssueEntity> testIssues = createTestIssueEntities();
        
        // 创建临时表
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When
        issueTempDao.batchInsert(testTempTableName, testIssues);
        
        // Then
        // 验证插入成功（这里可以添加查询验证逻辑）
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    @Test
    void testBatchInsertChunked() {
        // Given
        List<IssueEntity> testIssues = createTestIssueEntities();
        
        // 创建临时表
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When
        issueTempDao.batchInsertChunked(testTempTableName, testIssues);
        
        // Then
        // 验证插入成功
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    @Test
    void testMergeFromTempToTarget() {
        // Given
        List<IssueEntity> testIssues = createTestIssueEntities();
        
        // 创建临时表并插入数据
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        issueTempDao.batchInsert(testTempTableName, testIssues);
        
        // When
        int affectedRows = issueTempDao.mergeFromTempToTarget(testTargetTableName, testTempTableName);
        
        // Then
        // 验证 MERGE 操作结果
        assert affectedRows >= 0;
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    @Test
    void testDeleteFromTargetUsingTemp() {
        // Given
        List<IssueEntity> testIssues = createTestIssueEntities();
        
        // 创建临时表并插入数据
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        issueTempDao.batchInsert(testTempTableName, testIssues);
        
        // When
        int deletedRows = issueTempDao.deleteFromTargetUsingTemp(testTargetTableName, testTempTableName);
        
        // Then
        assert deletedRows >= 0;
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    @Test
    void testInsertFromTempToTarget() {
        // Given
        List<IssueEntity> testIssues = createTestIssueEntities();
        
        // 创建临时表并插入数据
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        issueTempDao.batchInsert(testTempTableName, testIssues);
        
        // When
        int insertedRows = issueTempDao.insertFromTempToTarget(testTargetTableName, testTempTableName);
        
        // Then
        assert insertedRows >= 0;
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    @Test
    void testLargeBatchInsert() {
        // Given - 创建大量测试数据
        List<IssueEntity> largeDataset = createLargeTestDataset(5000);
        
        // 创建临时表
        issueTempDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When - 分批插入
        int batchSize = 1000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < largeDataset.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, largeDataset.size());
            List<IssueEntity> batch = largeDataset.subList(i, endIndex);
            issueTempDao.batchInsertChunked(testTempTableName, batch);
        }
        
        long endTime = System.currentTimeMillis();
        
        // Then
        System.out.println("Large batch insert completed in " + (endTime - startTime) + " ms");
        
        // 清理
        issueTempDao.dropTable(testTempTableName);
    }

    private List<IssueEntity> createTestIssueEntities() {
        OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
        LocalDate today = LocalDate.now();
        
        IssueEntity issue1 = new IssueEntity(
                100001L, "Test Issue 1", "Open", "High", "Bug", now,
                "10001", "Test Project", "TEST", "TEST-001",
                "Internal", "Backend", "API", now.plusDays(30), "v1.0.0",
                today.plusDays(7), Decimal.valueOf(5), null, null,
                "Feature", "Web", now, now, "Testing",
                null, null, today.plusDays(14), "John Doe", "Jane Smith", "Bob Wilson",
                null, "Yes", "No", "QA Team A", "Development", null,
                null, null, "Jane Smith", null, null, null,
                today.plusDays(21), null, "Backend,API", null
        );

        IssueEntity issue2 = new IssueEntity(
                100002L, "Test Issue 2", "In Progress", "Medium", "Story", now.minusDays(1),
                "10001", "Test Project", "TEST", "TEST-002",
                "External", "Frontend", "UI", null, null,
                today.plusDays(10), Decimal.valueOf(3), null, null,
                "Enhancement", "Mobile", now, now, "Development",
                null, null, today.plusDays(20), "Alice Brown", "Charlie Davis", "Diana Evans",
                null, "No", "No", "QA Team B", "Feature", null,
                null, null, "Charlie Davis", null, null, null,
                today.plusDays(25), null, "Frontend,UI", null
        );

        IssueEntity issue3 = new IssueEntity(
                100003L, "Test Issue 3", "Done", "Low", "Task", now.minusDays(5),
                "10002", "Another Project", "ANOTHER", "ANOTHER-001",
                "Internal", "DevOps", "CI/CD", now.minusDays(1), "v1.1.0",
                today.minusDays(1), Decimal.valueOf(2), "Fixed", now.minusDays(1),
                "Infrastructure", "All", now, now, "Maintenance",
                null, null, today.minusDays(1), "Eve Foster", "Frank Green", "Grace Hill",
                480, "Yes", "No", "QA Team C", "Infrastructure", null,
                null, null, "Frank Green", "Additional testing required", null, 240,
                today.minusDays(1), "Completed successfully", "DevOps,CI/CD", "Verified"
        );

        return Arrays.asList(issue1, issue2, issue3);
    }

    private List<IssueEntity> createLargeTestDataset(int size) {
        List<IssueEntity> dataset = new java.util.ArrayList<>();
        OffsetDateTime now = OffsetDateTime.now(ZoneOffset.UTC);
        LocalDate today = LocalDate.now();
        
        for (int i = 0; i < size; i++) {
            IssueEntity issue = new IssueEntity(
                    (long) (200000 + i), "Large Test Issue " + i, "Open", "Medium", "Story", now,
                    "10001", "Large Test Project", "LARGE", "LARGE-" + String.format("%06d", i),
                    "Internal", "Backend", "API", now.plusDays(30), "v2.0.0",
                    today.plusDays(14), Decimal.valueOf(i % 10 + 1), null, null,
                    "Feature", "Web", now, now, "Development",
                    null, null, today.plusDays(21), "Developer " + (i % 10), "Tester " + (i % 5), "QA " + (i % 3),
                    null, "Yes", "No", "QA Team " + (char)('A' + i % 3), "Development", null,
                    null, null, "Assignee " + (i % 8), null, null, null,
                    today.plusDays(28), null, "Component" + (i % 5), null
            );
            dataset.add(issue);
        }
        
        return dataset;
    }
}
