package com.sayweee.datasync.dao;

import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LinkedIssueDao 测试类
 * 测试批量插入 SQL 的正确性和性能
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class LinkedIssueDaoTest {

    @Autowired
    private LinkedIssueDao linkedIssueDao;
    
    private String testTempTableName;
    private String testTargetTableName;

    @BeforeEach
    void setUp() {
        testTempTableName = "temp_test_linked_issues_" + System.currentTimeMillis();
        testTargetTableName = "weee_jira_new.issue_linked_issues";
    }

    @Test
    void testBatchInsert() {
        // Given
        List<LinkedIssueEntity> testLinkedIssues = createTestLinkedIssueEntities();
        
        // 创建临时表
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When
        linkedIssueDao.batchInsert(testTempTableName, testLinkedIssues);
        
        // Then
        // 验证插入成功（这里可以添加查询验证逻辑）
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    @Test
    void testBatchInsertChunked() {
        // Given
        List<LinkedIssueEntity> testLinkedIssues = createTestLinkedIssueEntities();
        
        // 创建临时表
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When
        linkedIssueDao.batchInsertChunked(testTempTableName, testLinkedIssues);
        
        // Then
        // 验证插入成功
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    @Test
    void testMergeFromTempToTarget() {
        // Given
        List<LinkedIssueEntity> testLinkedIssues = createTestLinkedIssueEntities();
        
        // 创建临时表并插入数据
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        linkedIssueDao.batchInsert(testTempTableName, testLinkedIssues);
        
        // When
        int affectedRows = linkedIssueDao.mergeFromTempToTarget(testTargetTableName, testTempTableName);
        
        // Then
        // 验证 MERGE 操作结果
        assertTrue(affectedRows >= 0);
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    @Test
    void testDeleteFromTargetUsingTemp() {
        // Given
        List<LinkedIssueEntity> testLinkedIssues = createTestLinkedIssueEntities();
        
        // 创建临时表并插入数据
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        linkedIssueDao.batchInsert(testTempTableName, testLinkedIssues);
        
        // When
        int deletedRows = linkedIssueDao.deleteFromTargetUsingTemp(testTargetTableName, testTempTableName);
        
        // Then
        assertTrue(deletedRows >= 0);
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    @Test
    void testInsertFromTempToTarget() {
        // Given
        List<LinkedIssueEntity> testLinkedIssues = createTestLinkedIssueEntities();
        
        // 创建临时表并插入数据
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        linkedIssueDao.batchInsert(testTempTableName, testLinkedIssues);
        
        // When
        int insertedRows = linkedIssueDao.insertFromTempToTarget(testTargetTableName, testTempTableName);
        
        // Then
        assertTrue(insertedRows >= 0);
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    @Test
    void testLargeBatchInsert() {
        // Given - 创建大量测试数据
        List<LinkedIssueEntity> largeDataset = createLargeTestDataset(1000);
        
        // 创建临时表
        linkedIssueDao.createTempTableLike(testTempTableName, testTargetTableName);
        
        // When - 分批插入
        int batchSize = 200;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < largeDataset.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, largeDataset.size());
            List<LinkedIssueEntity> batch = largeDataset.subList(i, endIndex);
            linkedIssueDao.batchInsertChunked(testTempTableName, batch);
        }
        
        long endTime = System.currentTimeMillis();
        
        // Then
        System.out.println("Large batch insert completed in " + (endTime - startTime) + " ms");
        
        // 清理
        linkedIssueDao.dropTable(testTempTableName);
    }

    /**
     * 创建测试用的 LinkedIssueEntity 列表
     */
    private List<LinkedIssueEntity> createTestLinkedIssueEntities() {
        LinkedIssueEntity entity1 = new LinkedIssueEntity();
        entity1.setIssueId(12345);
        entity1.setStoryKey("TEST-123");
        entity1.setLinkKey("TEST-456");
        entity1.setLinkType("blocks");

        LinkedIssueEntity entity2 = new LinkedIssueEntity();
        entity2.setIssueId(12346);
        entity2.setStoryKey("TEST-124");
        entity2.setLinkKey("TEST-457");
        entity2.setLinkType("relates to");

        LinkedIssueEntity entity3 = new LinkedIssueEntity();
        entity3.setIssueId(12347);
        entity3.setStoryKey("TEST-125");
        entity3.setLinkKey("TEST-458");
        entity3.setLinkType("is blocked by");

        return Arrays.asList(entity1, entity2, entity3);
    }

    /**
     * 创建大量测试数据
     */
    private List<LinkedIssueEntity> createLargeTestDataset(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> {
                    LinkedIssueEntity entity = new LinkedIssueEntity();
                    entity.setIssueId(100000 + i);
                    entity.setStoryKey("LARGE-" + String.format("%06d", i));
                    entity.setLinkKey("LINK-" + String.format("%06d", i + 1000));
                    entity.setLinkType(getLinkType(i % 4));
                    return entity;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    private String getLinkType(int index) {
        String[] linkTypes = {"blocks", "is blocked by", "relates to", "duplicates"};
        return linkTypes[index];
    }
}
