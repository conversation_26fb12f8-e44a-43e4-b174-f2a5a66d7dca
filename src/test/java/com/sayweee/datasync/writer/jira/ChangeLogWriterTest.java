package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.dao.ChangeLogDao;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ChangeLogWriter 测试类
 */
@ExtendWith(MockitoExtension.class)
class ChangeLogWriterTest {

    @Mock
    private ChangeLogDao changeLogDao;

    @InjectMocks
    private ChangeLogWriter changeLogWriter;

    private List<ChangeLogEntity> testChangeLogs;

    @BeforeEach
    void setUp() {
        testChangeLogs = createTestChangeLogEntities();
    }

    @Test
    void testWriteChangeLogs_WithValidData_ShouldProcessSuccessfully() {
        // Given
        when(changeLogDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(2);
        when(changeLogDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(3);

        // When
        assertDoesNotThrow(() -> changeLogWriter.writeChangeLogs(testChangeLogs));

        // Then
        verify(changeLogDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_changelogs"));
        verify(changeLogDao).batchInsert(anyString(), eq(testChangeLogs));
        verify(changeLogDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(changeLogDao).insertFromTempToTarget(anyString(), anyString());
        verify(changeLogDao).dropTable(anyString());
    }

    @Test
    void testWriteChangeLogs_WithNullData_ShouldReturnEarly() {
        // When
        changeLogWriter.writeChangeLogs(null);

        // Then
        verifyNoInteractions(changeLogDao);
    }

    @Test
    void testWriteChangeLogs_WithEmptyList_ShouldReturnEarly() {
        // When
        changeLogWriter.writeChangeLogs(Collections.emptyList());

        // Then
        verifyNoInteractions(changeLogDao);
    }

    @Test
    void testWriteChangeLogs_WhenCreateTempTableFails_ShouldThrowException() {
        // Given
        doThrow(new RuntimeException("Database error")).when(changeLogDao)
                .createTempTableLike(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> changeLogWriter.writeChangeLogs(testChangeLogs));
        
        assertTrue(exception.getMessage().contains("Failed to write change logs to database"));
    }

    @Test
    void testWriteChangeLogs_WhenBatchInsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Insert error")).when(changeLogDao)
                .batchInsert(anyString(), anyList());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> changeLogWriter.writeChangeLogs(testChangeLogs));
        
        assertTrue(exception.getMessage().contains("Failed to write change logs to database"));
        
        // Verify cleanup is attempted
        verify(changeLogDao).dropTable(anyString());
    }

    @Test
    void testWriteChangeLogs_WhenUpsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Upsert error")).when(changeLogDao)
                .deleteFromTargetUsingTemp(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> changeLogWriter.writeChangeLogs(testChangeLogs));
        
        assertTrue(exception.getMessage().contains("Failed to write change logs to database"));
        
        // Verify cleanup is attempted
        verify(changeLogDao).dropTable(anyString());
    }

    @Test
    void testWriteChangeLogs_WhenDropTableFails_ShouldNotThrowException() {
        // Given
        when(changeLogDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(1);
        when(changeLogDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(2);
        doThrow(new RuntimeException("Drop table error")).when(changeLogDao)
                .dropTable(anyString());

        // When & Then
        assertDoesNotThrow(() -> changeLogWriter.writeChangeLogs(testChangeLogs));
        
        // Verify all operations were attempted
        verify(changeLogDao).createTempTableLike(anyString(), anyString());
        verify(changeLogDao).batchInsert(anyString(), eq(testChangeLogs));
        verify(changeLogDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(changeLogDao).insertFromTempToTarget(anyString(), anyString());
        verify(changeLogDao).dropTable(anyString());
    }

    @Test
    void testWriteChangeLogs_WithLargeDataset_ShouldProcessSuccessfully() {
        // Given
        List<ChangeLogEntity> largeDataset = createLargeTestDataset(1000);
        when(changeLogDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(500);
        when(changeLogDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(1000);

        // When
        assertDoesNotThrow(() -> changeLogWriter.writeChangeLogs(largeDataset));

        // Then
        verify(changeLogDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_changelogs"));
        verify(changeLogDao).batchInsert(anyString(), eq(largeDataset));
        verify(changeLogDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(changeLogDao).insertFromTempToTarget(anyString(), anyString());
        verify(changeLogDao).dropTable(anyString());
    }

    /**
     * 创建测试用的 ChangeLogEntity 列表
     */
    private List<ChangeLogEntity> createTestChangeLogEntities() {
        ChangeLogEntity entity1 = new ChangeLogEntity();
        entity1.setChangeid(1001);
        entity1.setIssueId(12345);
        entity1.setUserid("user123");
        entity1.setUsername("Test User 1");
        entity1.setCreated(OffsetDateTime.now());
        entity1.setField("status");
        entity1.setFromValue("1");
        entity1.setFromString("Open");
        entity1.setToValue("2");
        entity1.setToString("In Progress");
        entity1.setInDate(LocalDateTime.now());

        ChangeLogEntity entity2 = new ChangeLogEntity();
        entity2.setChangeid(1002);
        entity2.setIssueId(12346);
        entity2.setUserid("user456");
        entity2.setUsername("Test User 2");
        entity2.setCreated(OffsetDateTime.now());
        entity2.setField("assignee");
        entity2.setFromValue("user123");
        entity2.setFromString("Old Assignee");
        entity2.setToValue("user456");
        entity2.setToString("New Assignee");
        entity2.setInDate(LocalDateTime.now());

        ChangeLogEntity entity3 = new ChangeLogEntity();
        entity3.setChangeid(1003);
        entity3.setIssueId(12347);
        entity3.setUserid("user789");
        entity3.setUsername("Test User 3");
        entity3.setCreated(OffsetDateTime.now());
        entity3.setField("priority");
        entity3.setFromValue("3");
        entity3.setFromString("Medium");
        entity3.setToValue("1");
        entity3.setToString("High");
        entity3.setInDate(LocalDateTime.now());

        return Arrays.asList(entity1, entity2, entity3);
    }

    /**
     * 创建大量测试数据
     */
    private List<ChangeLogEntity> createLargeTestDataset(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> {
                    ChangeLogEntity entity = new ChangeLogEntity();
                    entity.setChangeid(100000 + i);
                    entity.setIssueId(200000 + i);
                    entity.setUserid("user" + i);
                    entity.setUsername("Test User " + i);
                    entity.setCreated(OffsetDateTime.now().minusDays(i % 30));
                    entity.setField(getFieldType(i % 5));
                    entity.setFromValue("from_" + i);
                    entity.setFromString("From String " + i);
                    entity.setToValue("to_" + i);
                    entity.setToString("To String " + i);
                    entity.setInDate(LocalDateTime.now());
                    return entity;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    private String getFieldType(int index) {
        String[] fieldTypes = {"status", "assignee", "priority", "summary", "description"};
        return fieldTypes[index];
    }
}
