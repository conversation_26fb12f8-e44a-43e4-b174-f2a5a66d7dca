package com.sayweee.datasync.writer.jira;

import com.sayweee.datasync.dao.LinkedIssueDao;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LinkedIssueWriter 测试类
 */
@ExtendWith(MockitoExtension.class)
class LinkedIssueWriterTest {

    @Mock
    private LinkedIssueDao linkedIssueDao;

    @InjectMocks
    private LinkedIssueWriter linkedIssueWriter;

    private List<LinkedIssueEntity> testLinkedIssues;

    @BeforeEach
    void setUp() {
        testLinkedIssues = createTestLinkedIssueEntities();
    }

    @Test
    void testWrite_WithValidData_ShouldProcessSuccessfully() {
        // Given
        when(linkedIssueDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(2);
        when(linkedIssueDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(3);

        // When
        assertDoesNotThrow(() -> linkedIssueWriter.write(testLinkedIssues));

        // Then
        verify(linkedIssueDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_linked_issues"));
        verify(linkedIssueDao).batchInsert(anyString(), eq(testLinkedIssues));
        verify(linkedIssueDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(linkedIssueDao).insertFromTempToTarget(anyString(), anyString());
        verify(linkedIssueDao).dropTable(anyString());
    }

    @Test
    void testWrite_WithNullData_ShouldReturnEarly() {
        // When
        linkedIssueWriter.write(null);

        // Then
        verifyNoInteractions(linkedIssueDao);
    }

    @Test
    void testWrite_WithEmptyList_ShouldReturnEarly() {
        // When
        linkedIssueWriter.write(Collections.emptyList());

        // Then
        verifyNoInteractions(linkedIssueDao);
    }

    @Test
    void testWrite_WhenCreateTempTableFails_ShouldThrowException() {
        // Given
        doThrow(new RuntimeException("Database error")).when(linkedIssueDao)
                .createTempTableLike(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> linkedIssueWriter.write(testLinkedIssues));
        
        assertTrue(exception.getMessage().contains("Failed to write linked issues to database"));
    }

    @Test
    void testWrite_WhenBatchInsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Insert error")).when(linkedIssueDao)
                .batchInsert(anyString(), anyList());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> linkedIssueWriter.write(testLinkedIssues));
        
        assertTrue(exception.getMessage().contains("Failed to write linked issues to database"));
        
        // Verify cleanup is attempted
        verify(linkedIssueDao).dropTable(anyString());
    }

    @Test
    void testWrite_WhenUpsertFails_ShouldThrowExceptionAndCleanup() {
        // Given
        doThrow(new RuntimeException("Upsert error")).when(linkedIssueDao)
                .deleteFromTargetUsingTemp(anyString(), anyString());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, 
                () -> linkedIssueWriter.write(testLinkedIssues));
        
        assertTrue(exception.getMessage().contains("Failed to write linked issues to database"));
        
        // Verify cleanup is attempted
        verify(linkedIssueDao).dropTable(anyString());
    }

    @Test
    void testWrite_WhenDropTableFails_ShouldNotThrowException() {
        // Given
        when(linkedIssueDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(1);
        when(linkedIssueDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(2);
        doThrow(new RuntimeException("Drop table error")).when(linkedIssueDao)
                .dropTable(anyString());

        // When & Then
        assertDoesNotThrow(() -> linkedIssueWriter.write(testLinkedIssues));
        
        // Verify all operations were attempted
        verify(linkedIssueDao).createTempTableLike(anyString(), anyString());
        verify(linkedIssueDao).batchInsert(anyString(), eq(testLinkedIssues));
        verify(linkedIssueDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(linkedIssueDao).insertFromTempToTarget(anyString(), anyString());
        verify(linkedIssueDao).dropTable(anyString());
    }

    @Test
    void testWrite_WithLargeDataset_ShouldProcessSuccessfully() {
        // Given
        List<LinkedIssueEntity> largeDataset = createLargeTestDataset(1000);
        when(linkedIssueDao.deleteFromTargetUsingTemp(anyString(), anyString())).thenReturn(500);
        when(linkedIssueDao.insertFromTempToTarget(anyString(), anyString())).thenReturn(1000);

        // When
        assertDoesNotThrow(() -> linkedIssueWriter.write(largeDataset));

        // Then
        verify(linkedIssueDao).createTempTableLike(anyString(), eq("weee_jira_new.issue_linked_issues"));
        verify(linkedIssueDao).batchInsert(anyString(), eq(largeDataset));
        verify(linkedIssueDao).deleteFromTargetUsingTemp(anyString(), anyString());
        verify(linkedIssueDao).insertFromTempToTarget(anyString(), anyString());
        verify(linkedIssueDao).dropTable(anyString());
    }

    /**
     * 创建测试用的 LinkedIssueEntity 列表
     */
    private List<LinkedIssueEntity> createTestLinkedIssueEntities() {
        LinkedIssueEntity entity1 = new LinkedIssueEntity();
        entity1.setIssueId(12345);
        entity1.setStoryKey("TEST-123");
        entity1.setLinkKey("TEST-456");
        entity1.setLinkType("blocks");

        LinkedIssueEntity entity2 = new LinkedIssueEntity();
        entity2.setIssueId(12346);
        entity2.setStoryKey("TEST-124");
        entity2.setLinkKey("TEST-457");
        entity2.setLinkType("relates to");

        LinkedIssueEntity entity3 = new LinkedIssueEntity();
        entity3.setIssueId(12347);
        entity3.setStoryKey("TEST-125");
        entity3.setLinkKey("TEST-458");
        entity3.setLinkType("is blocked by");

        return Arrays.asList(entity1, entity2, entity3);
    }

    /**
     * 创建大量测试数据
     */
    private List<LinkedIssueEntity> createLargeTestDataset(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> {
                    LinkedIssueEntity entity = new LinkedIssueEntity();
                    entity.setIssueId(100000 + i);
                    entity.setStoryKey("LARGE-" + String.format("%06d", i));
                    entity.setLinkKey("LINK-" + String.format("%06d", i + 1000));
                    entity.setLinkType(getLinkType(i % 4));
                    return entity;
                })
                .collect(java.util.stream.Collectors.toList());
    }

    private String getLinkType(int index) {
        String[] linkTypes = {"blocks", "is blocked by", "relates to", "duplicates"};
        return linkTypes[index];
    }
}
