# Redshift 批量写入功能

## 概述

本文档描述了 `IssueWriter` 类中实现的批量写入或更新 Redshift 记录的功能。该功能使用高效的批量操作和 UPSERT 策略来处理大量 JIRA 数据的同步。

## 核心特性

### 1. 批量 UPSERT 操作
- 使用临时表和 MERGE 语句实现高效的批量 UPSERT
- 支持插入新记录和更新现有记录
- 自动处理数据冲突和重复

### 2. 事务管理
- 所有操作都在事务中执行，确保数据一致性
- 失败时自动回滚，保证数据完整性

### 3. 同步状态跟踪
- 集成 `SyncMetadataService` 跟踪同步任务状态
- 记录处理统计信息（成功、失败、总数）
- 支持错误记录和状态查询

### 4. 性能优化
- 使用 HikariCP 连接池管理数据库连接
- 批量操作减少网络往返次数
- 临时表策略提高大批量数据处理效率

## 架构设计

```
JiraService -> IssueWriter -> Redshift
     |              |
     |              +-> SyncMetadataService
     |
     +-> IssueFetcher -> JiraPayloadParser
```

### 核心组件

1. **IssueWriter**: 主要的批量写入组件
2. **SyncMetadataService**: 同步元数据管理
3. **DatabaseConfig**: 数据库连接配置
4. **JdbcTemplate**: Spring JDBC 模板

## 使用方法

### 1. 基本用法

```java
@Autowired
private IssueWriter issueWriter;

// 批量写入 JIRA Issues
List<JiraIngestionPayload> payloads = fetchJiraData();
String taskId = "jira-sync-" + System.currentTimeMillis();
issueWriter.write(taskId, payloads);
```

### 2. 配置数据库连接

在 `application.yml` 中配置 Redshift 连接：

```yaml
datasync:
  database:
    redshift:
      url: ****************************************************************
      username: your-username
      password: your-password
      pool:
        maximum-pool-size: 20
        minimum-idle: 5
```

### 3. 环境变量配置

```bash
export REDSHIFT_URL="****************************************************************"
export REDSHIFT_USERNAME="your-username"
export REDSHIFT_PASSWORD="your-password"
```

## 数据库表结构

### jira_issues 表

```sql
CREATE TABLE jira_issues (
    id BIGINT NOT NULL,
    summary VARCHAR(1000),
    status VARCHAR(100),
    priority VARCHAR(100),
    issuetype VARCHAR(100),
    created TIMESTAMP,
    project_id VARCHAR(50),
    project_name VARCHAR(200),
    project_key VARCHAR(50),
    issue_key VARCHAR(100) NOT NULL,
    -- ... 其他字段
    PRIMARY KEY (id)
)
DISTSTYLE KEY
DISTKEY (project_key)
SORTKEY (created, updated, id);
```

### sync_metadata 表

```sql
CREATE TABLE sync_metadata (
    id BIGINT IDENTITY(1,1),
    task_id VARCHAR(100) NOT NULL,
    sync_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    records_processed INTEGER DEFAULT 0,
    records_success INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    error_message VARCHAR(2000),
    PRIMARY KEY (id)
);
```

## 批量操作流程

### 1. 创建临时表
```sql
CREATE TEMP TABLE temp_jira_issues_[taskId]_[timestamp] (
    -- 与目标表相同的结构
);
```

### 2. 批量插入到临时表
```java
jdbcTemplate.batchUpdate(insertSql, payloads, batchSize, parameterSetter);
```

### 3. 执行 MERGE 操作
```sql
MERGE INTO jira_issues AS target
USING temp_jira_issues_[taskId]_[timestamp] AS source
ON target.id = source.id
WHEN MATCHED THEN UPDATE SET ...
WHEN NOT MATCHED THEN INSERT ...
```

### 4. 清理临时表
```sql
DROP TABLE IF EXISTS temp_jira_issues_[taskId]_[timestamp];
```

## 性能调优

### 1. 批量大小配置
```yaml
sync:
  constants:
    defaults:
      redshift-batch-size: 1000  # 调整批量大小
```

### 2. 连接池配置
```yaml
datasync:
  database:
    redshift:
      pool:
        maximum-pool-size: 50    # 增加连接池大小
        minimum-idle: 10
```

### 3. JVM 参数优化
```bash
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-Xmx4g
-Xms2g
```

## 监控和日志

### 1. 同步状态查询
```java
SyncStatus status = syncMetadataService.getSyncTaskStatus(taskId);
```

### 2. 日志级别配置
```yaml
logging:
  level:
    com.sayweee.datasync.writer: DEBUG
    org.springframework.jdbc: WARN
```

### 3. 关键指标监控
- 批量处理时间
- 成功/失败记录数
- 数据库连接池使用率
- 内存使用情况

## 错误处理

### 1. 数据库连接错误
- 自动重试机制
- 连接池健康检查
- 失败时记录详细错误信息

### 2. 数据格式错误
- 参数验证和类型转换
- NULL 值安全处理
- 字符串长度截断

### 3. 事务回滚
- 批量操作失败时自动回滚
- 保持数据一致性
- 记录失败原因

## 最佳实践

### 1. 批量大小选择
- 根据数据大小和网络条件调整
- 建议范围：500-2000 条记录

### 2. 事务超时设置
- 根据数据量设置合适的超时时间
- 避免长时间锁定资源

### 3. 监控和告警
- 设置同步任务监控
- 配置失败告警机制
- 定期检查数据一致性

### 4. 资源管理
- 及时清理临时表
- 监控连接池使用情况
- 合理设置内存参数

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 调整连接超时参数
   - 验证 Redshift 集群状态

2. **内存不足**
   - 减少批量大小
   - 增加 JVM 堆内存
   - 优化数据处理逻辑

3. **数据类型错误**
   - 检查字段映射
   - 验证数据格式
   - 添加数据验证逻辑

### 调试步骤

1. 启用详细日志
2. 检查同步元数据表
3. 验证数据库连接
4. 分析错误堆栈信息
5. 检查 Redshift 查询历史
