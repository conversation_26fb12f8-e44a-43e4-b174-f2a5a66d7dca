# MyBatis 批量插入 SQL 实现

## 概述

本文档详细说明了为 `IssueTempDao.batchInsert` 方法生成的 MyBatis 批量插入 SQL 实现。该实现支持高效的批量数据插入操作，适用于大规模 JIRA 数据同步场景。

## SQL 实现

### 1. 基础批量插入

```xml
<insert id="batchInsert">
    INSERT INTO ${tableName} (
        id, summary, status, priority, issuetype, created, project_id, project_name, project_key, issue_key,
        issue_source, issue_module, issue_sub_module, fix_version_date, fix_version, due_date, story_points,
        resolution, resolved, issue_userview_category, issue_affects_platform, updated, in_date, purpose,
        rfq_plan_date, rfq_date, plan_release_date, reporter, developer, tester, original_estimate,
        smoke_check, legacy_issue, qa_team, work_category, parent_key, rfq_delay_comment,
        release_delay_comment, assignee, additional_requirements, parent_id, time_spent, eta_shanghai,
        remark, components, bug_description_check
    ) VALUES
    <foreach collection="payloads" item="item" separator=",">
        (
            #{item.id}, #{item.summary}, #{item.status}, #{item.priority}, #{item.issuetype},
            #{item.created}, #{item.projectId}, #{item.projectName}, #{item.projectKey}, #{item.issueKey},
            #{item.issueSource}, #{item.issueModule}, #{item.issueSubModule}, #{item.fixVersionDate},
            #{item.fixVersion}, #{item.dueDate}, #{item.storyPoints}, #{item.resolution}, #{item.resolved},
            #{item.issueUserviewCategory}, #{item.issueAffectsPlatform}, #{item.updated}, #{item.inDate},
            #{item.purpose}, #{item.rfqPlanDate}, #{item.rfqDate}, #{item.planReleaseDate}, #{item.reporter},
            #{item.developer}, #{item.tester}, #{item.originalEstimate}, #{item.smokeCheck}, #{item.legacyIssue},
            #{item.qaTeam}, #{item.workCategory}, #{item.parentKey}, #{item.rfqDelayComment},
            #{item.releaseDelayComment}, #{item.assignee}, #{item.additionalRequirements}, #{item.parentId},
            #{item.timeSpent}, #{item.etaShanghai}, #{item.remark}, #{item.components}, #{item.bugDescriptionCheck}
        )
    </foreach>
</insert>
```

### 2. 优化版本（带类型声明）

```xml
<insert id="batchInsertChunked" parameterType="map">
    INSERT INTO ${tableName} (
        id, summary, status, priority, issuetype, created, project_id, project_name, project_key, issue_key,
        issue_source, issue_module, issue_sub_module, fix_version_date, fix_version, due_date, story_points,
        resolution, resolved, issue_userview_category, issue_affects_platform, updated, in_date, purpose,
        rfq_plan_date, rfq_date, plan_release_date, reporter, developer, tester, original_estimate,
        smoke_check, legacy_issue, qa_team, work_category, parent_key, rfq_delay_comment,
        release_delay_comment, assignee, additional_requirements, parent_id, time_spent, eta_shanghai,
        remark, components, bug_description_check
    ) VALUES
    <foreach collection="payloads" item="item" separator=",">
        (
            #{item.id,jdbcType=BIGINT}, #{item.summary,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR},
            #{item.priority,jdbcType=VARCHAR}, #{item.issuetype,jdbcType=VARCHAR}, #{item.created,jdbcType=TIMESTAMP},
            #{item.projectId,jdbcType=VARCHAR}, #{item.projectName,jdbcType=VARCHAR}, #{item.projectKey,jdbcType=VARCHAR},
            #{item.issueKey,jdbcType=VARCHAR}, #{item.issueSource,jdbcType=VARCHAR}, #{item.issueModule,jdbcType=VARCHAR},
            #{item.issueSubModule,jdbcType=VARCHAR}, #{item.fixVersionDate,jdbcType=TIMESTAMP}, #{item.fixVersion,jdbcType=VARCHAR},
            #{item.dueDate,jdbcType=DATE}, #{item.storyPoints,jdbcType=DECIMAL}, #{item.resolution,jdbcType=VARCHAR},
            #{item.resolved,jdbcType=TIMESTAMP}, #{item.issueUserviewCategory,jdbcType=VARCHAR}, #{item.issueAffectsPlatform,jdbcType=VARCHAR},
            #{item.updated,jdbcType=TIMESTAMP}, #{item.inDate,jdbcType=TIMESTAMP}, #{item.purpose,jdbcType=VARCHAR},
            #{item.rfqPlanDate,jdbcType=TIMESTAMP}, #{item.rfqDate,jdbcType=TIMESTAMP}, #{item.planReleaseDate,jdbcType=DATE},
            #{item.reporter,jdbcType=VARCHAR}, #{item.developer,jdbcType=VARCHAR}, #{item.tester,jdbcType=VARCHAR},
            #{item.originalEstimate,jdbcType=INTEGER}, #{item.smokeCheck,jdbcType=VARCHAR}, #{item.legacyIssue,jdbcType=VARCHAR},
            #{item.qaTeam,jdbcType=VARCHAR}, #{item.workCategory,jdbcType=VARCHAR}, #{item.parentKey,jdbcType=VARCHAR},
            #{item.rfqDelayComment,jdbcType=VARCHAR}, #{item.releaseDelayComment,jdbcType=VARCHAR}, #{item.assignee,jdbcType=VARCHAR},
            #{item.additionalRequirements,jdbcType=VARCHAR}, #{item.parentId,jdbcType=INTEGER}, #{item.timeSpent,jdbcType=INTEGER},
            #{item.etaShanghai,jdbcType=DATE}, #{item.remark,jdbcType=VARCHAR}, #{item.components,jdbcType=VARCHAR},
            #{item.bugDescriptionCheck,jdbcType=VARCHAR}
        )
    </foreach>
</insert>
```

### 3. MERGE 操作（UPSERT）

```xml
<update id="mergeFromTempToTarget">
    MERGE INTO ${targetTable} AS target
    USING ${tempTable} AS source
    ON target.id = source.id
    WHEN MATCHED THEN
        UPDATE SET
            summary = source.summary,
            status = source.status,
            priority = source.priority,
            -- ... 其他字段
    WHEN NOT MATCHED THEN
        INSERT (id, summary, status, priority, ...)
        VALUES (source.id, source.summary, source.status, source.priority, ...)
</update>
```

## 字段映射

### IssueEntity 字段到数据库列的映射

| Java 字段 | 数据库列 | 类型 | 说明 |
|-----------|----------|------|------|
| `id` | `id` | BIGINT | 主键 |
| `summary` | `summary` | VARCHAR(1000) | 问题摘要 |
| `status` | `status` | VARCHAR(100) | 状态 |
| `priority` | `priority` | VARCHAR(100) | 优先级 |
| `issuetype` | `issuetype` | VARCHAR(100) | 问题类型 |
| `created` | `created` | TIMESTAMP | 创建时间 |
| `projectId` | `project_id` | VARCHAR(50) | 项目ID |
| `projectName` | `project_name` | VARCHAR(200) | 项目名称 |
| `projectKey` | `project_key` | VARCHAR(50) | 项目键 |
| `issueKey` | `issue_key` | VARCHAR(100) | 问题键 |
| `issueSource` | `issue_source` | VARCHAR(200) | 问题来源 |
| `issueModule` | `issue_module` | VARCHAR(200) | 问题模块 |
| `issueSubModule` | `issue_sub_module` | VARCHAR(200) | 问题子模块 |
| `fixVersionDate` | `fix_version_date` | TIMESTAMP | 修复版本日期 |
| `fixVersion` | `fix_version` | VARCHAR(200) | 修复版本 |
| `dueDate` | `due_date` | DATE | 截止日期 |
| `storyPoints` | `story_points` | DECIMAL(10,2) | 故事点 |
| `resolution` | `resolution` | VARCHAR(100) | 解决方案 |
| `resolved` | `resolved` | TIMESTAMP | 解决时间 |
| `issueUserviewCategory` | `issue_userview_category` | VARCHAR(200) | 用户视图分类 |
| `issueAffectsPlatform` | `issue_affects_platform` | VARCHAR(200) | 影响平台 |
| `updated` | `updated` | TIMESTAMP | 更新时间 |
| `inDate` | `in_date` | TIMESTAMP | 录入时间 |
| `purpose` | `purpose` | VARCHAR(500) | 目的 |
| `rfqPlanDate` | `rfq_plan_date` | TIMESTAMP | RFQ计划日期 |
| `rfqDate` | `rfq_date` | TIMESTAMP | RFQ日期 |
| `planReleaseDate` | `plan_release_date` | DATE | 计划发布日期 |
| `reporter` | `reporter` | VARCHAR(200) | 报告人 |
| `developer` | `developer` | VARCHAR(200) | 开发人员 |
| `tester` | `tester` | VARCHAR(200) | 测试人员 |
| `originalEstimate` | `original_estimate` | INTEGER | 原始估算 |
| `smokeCheck` | `smoke_check` | VARCHAR(100) | 冒烟测试 |
| `legacyIssue` | `legacy_issue` | VARCHAR(100) | 遗留问题 |
| `qaTeam` | `qa_team` | VARCHAR(200) | QA团队 |
| `workCategory` | `work_category` | VARCHAR(200) | 工作分类 |
| `parentKey` | `parent_key` | VARCHAR(100) | 父问题键 |
| `rfqDelayComment` | `rfq_delay_comment` | VARCHAR(2000) | RFQ延迟备注 |
| `releaseDelayComment` | `release_delay_comment` | VARCHAR(2000) | 发布延迟备注 |
| `assignee` | `assignee` | VARCHAR(200) | 经办人 |
| `additionalRequirements` | `additional_requirements` | VARCHAR(2000) | 额外需求 |
| `parentId` | `parent_id` | INTEGER | 父问题ID |
| `timeSpent` | `time_spent` | INTEGER | 花费时间 |
| `etaShanghai` | `eta_shanghai` | DATE | 上海ETA |
| `remark` | `remark` | VARCHAR(2000) | 备注 |
| `components` | `components` | VARCHAR(1000) | 组件 |
| `bugDescriptionCheck` | `bug_description_check` | VARCHAR(2000) | Bug描述检查 |

## 使用方法

### 1. DAO 接口调用

```java
@Autowired
private IssueTempDao issueTempDao;

// 基础批量插入
List<IssueEntity> issues = getIssueEntities();
String tempTableName = "temp_jira_issues_" + System.currentTimeMillis();
issueTempDao.batchInsert(tempTableName, issues);

// 优化版本批量插入
issueTempDao.batchInsertChunked(tempTableName, issues);

// MERGE 操作
int affectedRows = issueTempDao.mergeFromTempToTarget("jira_issues", tempTableName);
```

### 2. 分批处理大数据集

```java
public void batchInsertLargeDataset(String tableName, List<IssueEntity> allIssues) {
    int batchSize = 1000; // 每批处理1000条记录
    
    for (int i = 0; i < allIssues.size(); i += batchSize) {
        int endIndex = Math.min(i + batchSize, allIssues.size());
        List<IssueEntity> batch = allIssues.subList(i, endIndex);
        
        try {
            issueTempDao.batchInsertChunked(tableName, batch);
            log.info("Successfully inserted batch {}-{}", i, endIndex);
        } catch (Exception e) {
            log.error("Failed to insert batch {}-{}", i, endIndex, e);
            // 处理错误或重试
        }
    }
}
```

## 性能优化建议

### 1. 批量大小调优

```java
// 根据数据库和网络条件调整批量大小
int optimalBatchSize = 500; // 推荐范围：200-1000

// 对于 Redshift，可以使用更大的批量
int redshiftBatchSize = 2000; // 推荐范围：1000-5000
```

### 2. MyBatis 配置优化

```xml
<!-- mybatis-config.xml -->
<configuration>
    <settings>
        <!-- 启用批量执行器 -->
        <setting name="defaultExecutorType" value="BATCH"/>
        <!-- 设置批量大小 -->
        <setting name="defaultStatementTimeout" value="300"/>
    </settings>
</configuration>
```

### 3. 数据库连接优化

```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      batch-size: 1000
      connection-timeout: 30000
```

## 错误处理

### 1. 数据类型转换错误

```java
// 确保 Java 类型与数据库类型匹配
// 使用 jdbcType 明确指定类型映射
#{item.storyPoints,jdbcType=DECIMAL}
#{item.created,jdbcType=TIMESTAMP}
```

### 2. NULL 值处理

```xml
<!-- 在 SQL 中处理 NULL 值 -->
<if test="item.storyPoints != null">
    #{item.storyPoints,jdbcType=DECIMAL}
</if>
<if test="item.storyPoints == null">
    NULL
</if>
```

### 3. 字符串长度超限

```java
// 在插入前验证和截断字符串
public String truncateString(String value, int maxLength) {
    return value != null && value.length() > maxLength 
        ? value.substring(0, maxLength) 
        : value;
}
```

## 监控和调试

### 1. 启用 SQL 日志

```yaml
logging:
  level:
    com.sayweee.datasync.dao: DEBUG
    org.apache.ibatis: DEBUG
```

### 2. 性能监控

```java
@Component
public class BatchInsertMonitor {
    
    @EventListener
    public void onBatchInsert(BatchInsertEvent event) {
        log.info("Batch insert completed: {} records in {} ms", 
            event.getRecordCount(), event.getDuration());
    }
}
```

### 3. 错误统计

```java
public class BatchInsertStats {
    private int totalRecords;
    private int successRecords;
    private int failedRecords;
    private long totalTime;
    
    // 统计方法...
}
```

## 最佳实践

1. **合理设置批量大小**：根据数据库性能和网络条件调整
2. **使用事务管理**：确保批量操作的原子性
3. **错误恢复机制**：实现失败重试和部分成功处理
4. **监控和告警**：跟踪批量操作的性能和成功率
5. **数据验证**：在插入前验证数据完整性和格式
6. **资源管理**：及时释放数据库连接和内存资源
