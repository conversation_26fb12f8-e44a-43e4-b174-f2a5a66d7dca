FROM weee-img:21-jdk-aws

# Install curl for health checks (if not already available)
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/* || true

WORKDIR /app

# Copy the JAR file
ADD ./target/core-datasync*.jar /app/core-datasync.jar

# Create non-root user for security (if not exists)
RUN groupadd -r datasync && useradd -r -g datasync datasync || true
RUN chown -R datasync:datasync /app || true

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# JVM optimization for containers
ENV JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication"

# Run the application
CMD ["sh", "-c", "java $JAVA_OPTS -jar /app/core-datasync.jar"]